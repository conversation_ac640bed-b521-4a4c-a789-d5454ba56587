<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php echo $site_name ?? ""; ?> - 免费发布分类信息</title>
    <meta name="keywords" content="{$site_keywords|default('分类信息,免费发布信息')}">
    <meta name="description" content="{$site_description|default('提供免费发布分类信息服务的网站')}">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/index.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/list-item-clickable.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
    <style>
    /* 简约主题头部样式 */
    html.theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee;
    }
    
    html.theme-simple header .header-title,
    html.theme-simple header .menu-icon i,
    html.theme-simple header .header-actions a {
        color: #333333 !important;
    }
    
    html.theme-simple .header-inner,
    html.theme-simple .sticky-inner {
        background-color: #ffffff !important;
    }
    
    html.theme-simple .search-box {
        background-color: #f5f5f5 !important;
        color: #666666 !important;
    }
    </style>
  
</head>
<body>
    <!-- 普通头部 -->
    <header class="main-header">
        <div class="container">
            <div class="header-inner">
                <div class="menu-icon">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box" onclick="toggleSearch()">
                    <i class="fas fa-search"></i>
                    <span class="search-placeholder">找工作 找房子 找服务</span>
                </div>
                <div class="header-actions">
                    <a href="/post.php" class="add-btn">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>
    
    <!-- 固定搜索头部 -->
    <header class="sticky-header" id="stickyHeader">
        <div class="container">
            <div class="sticky-inner">
                <div class="menu-icon">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="search-box" onclick="toggleSearch()">
                    <i class="fas fa-search"></i>
                    <span class="search-placeholder">搜索信息...</span>
                </div>
                <div class="header-actions">
                    <a href="/post.php" class="add-btn">
                        <i class="fas fa-plus"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- 搜索层 -->
    <div class="search-layer" id="searchLayer">
        <div class="search-header">
            <a href="javascript:void(0);" class="search-back" onclick="toggleSearch()">
                <i class="fas fa-chevron-left"></i>
            </a>
            <form action="/search.php" method="get" class="search-form" id="home-search-form">
                <i class="fas fa-search search-icon" id="search-icon"></i>
                <i class="fas fa-spinner fa-spin search-loading" id="search-loading-icon" style="display: none;"></i>
                <input type="text" name="keyword" class="search-input" placeholder="搜索工作、房子、手机..." autocomplete="off" id="home-search-input">
            </form>
            <button type="button" class="search-cancel" onclick="toggleSearch()">取消</button>
        </div>
        <div class="search-content">
            <div class="search-history">
                <div class="search-section-title">
                    <span>搜索历史</span>
                    <button type="button" class="search-clear" onclick="clearSearchHistory()">
                        <i class="fas fa-trash-alt"></i> 清空
                    </button>
                </div>
                <div class="search-tags" id="searchHistoryTags"></div>
            </div>
            <div class="search-hot">
                <div class="search-section-title">
                    <span>热门搜索</span>
                </div>
                <div class="search-tags">
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">招聘</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">兼职</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">房屋出租</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">二手车</a>
                    <a href="javascript:void(0);" class="search-tag" onclick="searchTag(this)">手机</a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="category-section">
            <div class="category-scroll">
                <div class="category-grid-container">
                    <div class="category-grid">
                        <!-- 第一页：前10个分类 -->
                        <!-- 动态显示所有分类 -->
                    <?php
                    $totalCategories = count($categories);
                    $firstPageCount = min(10, $totalCategories); // 第一页最多显示10个分类
                    
                    // 显示第一页分类
                    for ($i = 0; $i < $firstPageCount; $i++) {
                        $category = $categories[$i];
                        $iconClass = !empty($category['icon']) ? $category['icon'] : 'fa-tag';
                        
                        // 根据分类名称确定CSS类
                        $cssClass = getCategoryCssClass($category['name']);
                        
                        echo '<a href="/' . $category['pinyin'] . '/" class="category-item">';
                        echo '<div class="category-icon ' . $cssClass . '">';
                        echo '<i class="fas ' . $iconClass . '"></i>';
                        echo '</div>';
                        echo '<div class="category-name">' . $category['name'] . '</div>';
                        echo '</a>';
                    }
                    
                    // 辅助函数 - 根据分类名称获取CSS类
                    function getCategoryCssClass($name) {
                        $mappings = [
                            '房' => 'house',
                            '屋' => 'house',
                            '租' => 'house',
                            '职' => 'job',
                            '聘' => 'job',
                            '工作' => 'job',
                            '车' => 'car',
                            '教育' => 'edu',
                            '培训' => 'edu',
                            '服务' => 'service',
                            '宠物' => 'pet',
                            '手机' => 'phone',
                            '数码' => 'phone',
                            '家居' => 'furniture',
                            '家具' => 'furniture',
                            '维修' => 'repair',
                            '食' => 'food',
                            '餐' => 'food'
                        ];
                        
                        foreach ($mappings as $keyword => $class) {
                            if (mb_strpos($name, $keyword) !== false) {
                                return $class;
                            }
                        }
                        
                        return 'more'; // 默认样式
                    }
                    ?>
                    </div>
                    
                    <!-- 其他分类，通过滑动查看 -->
                    <div class="category-grid" style="margin-left: 15px;">
                        <a href="/ershou/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="category-name">二手物品</div>
                        </a>
                        <a href="/qiche/" class="category-item">
                            <div class="category-icon car">
                                <i class="fas fa-car-side"></i>
                            </div>
                            <div class="category-name">汽车服务</div>
                        </a>
                        <a href="/jianli/" class="category-item">
                            <div class="category-icon job">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="category-name">简历资料</div>
                        </a>
                        <a href="/huazhuang/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-spa"></i>
                            </div>
                            <div class="category-name">美妆护肤</div>
                        </a>
                        <a href="/yule/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-film"></i>
                            </div>
                            <div class="category-name">休闲娱乐</div>
                        </a>
                        <a href="/lvyou/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-plane"></i>
                            </div>
                            <div class="category-name">旅游出行</div>
                        </a>
                        <a href="/muying/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-baby"></i>
                            </div>
                            <div class="category-name">母婴亲子</div>
                        </a>
                        <a href="/jiadian/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-tv"></i>
                            </div>
                            <div class="category-name">家用电器</div>
                        </a>
                        <a href="/baojie/" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-broom"></i>
                            </div>
                            <div class="category-name">保洁清洗</div>
                        </a>
                        <a href="/category.php" class="category-item">
                            <div class="category-icon more">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                            <div class="category-name">更多分类</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-container">
            <div class="tab-header">
                <div class="tab-title">最新信息</div>
            </div>
            
            <div id="tab-text" class="tab-content">
                <div class="simple-list">
                    <?php if(null !== ($topPosts ?? null) && !empty($topPosts)): ?>
                    <?php if(null !== ($topPosts ?? null) && is_array($topPosts)): foreach($topPosts as $post): ?>
                    <?php
                    // 计算有效期
                    if (!empty($post['expired_at'])) {
                        $days = getRemainingDaysInt($post['expired_at']);
                        $validity = $days > 0 ? $days.'天' : '已过期';
                    } else {
                        $validity = '长期';
                    }
                    ?>
                    <div class="list-item is-top">
                        <div class="item-row">
                            <div class="item-left">
                                <div class="item-title">
                                    <?php if ($validity == '已过期'): ?>
                                    <a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="expired"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?><span class="top-tag">顶</span></a>
                                    <?php else: ?>
                                    <a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?><span class="top-tag">顶</span></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="item-right">
                                <div class="item-time">
                                    <?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; endif; ?>
                    <?php endif; ?>
                    <?php if(null !== ($normalPosts ?? null) && !empty($normalPosts)): ?>
                    <?php if(null !== ($normalPosts ?? null) && is_array($normalPosts)): foreach($normalPosts as $post): ?>
                    <?php
                    // 计算有效期
                    if (!empty($post['expired_at'])) {
                        $days = getRemainingDaysInt($post['expired_at']);
                        $validity = $days > 0 ? $days.'天' : '已过期';
                    } else {
                        $validity = '长期';
                    }
                    ?>
                    <div class="list-item">
                        <div class="item-row">
                            <div class="item-left">
                                <div class="item-title">
                                    <?php if ($validity == '已过期'): ?>
                                    <a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="expired"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
                                    <?php else: ?>
                                    <a href="/<?php echo (isset($post['category_pinyin'])) ? $post['category_pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="item-right">
                                <div class="item-time">
                                    <?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; endif; ?>
                <?php endif; ?>
            </div>
        </div>


        </div>
    </div>

    <!-- 移动端底部导航栏 -->
<nav class="navbar">
    <a href="/" class="nav-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-home"></i></span>
        <span class="nav-text">首页</span>
    </a>
    <a href="/category.php" class="nav-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-th-large"></i></span>
        <span class="nav-text">分类</span>
    </a>
    <a href="/post.php" class="nav-item publish <?php if($current_page == 'post'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-plus"></i></span>
        <span class="nav-text">发布</span>
    </a>
    <a href="/message.php" class="nav-item <?php if($current_page == 'message'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-comment-alt"></i></span>
        <span class="nav-text">消息</span>
    </a>
    <a href="/member/" class="nav-item <?php if($current_page == 'member'): ?>active<?php endif; ?>">
        <span class="nav-icon"><i class="fas fa-user"></i></span>
        <span class="nav-text">我的</span>
    </a>
</nav>


    <footer>
    <div class="container">
       {literal}{block:{/literal}m_footer_nav{literal}}{/literal}
        <div class="theme-switcher">
            <p>主题切换</p>
            <div class="theme-dots">
                <a href="javascript:void(0);" onclick="switchTheme('red')" class="theme-dot theme-red" title="红色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('blue')" class="theme-dot theme-blue" title="蓝色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('green')" class="theme-dot theme-green" title="绿色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('purple')" class="theme-dot theme-purple" title="紫色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('orange')" class="theme-dot theme-orange" title="橙色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('pink')" class="theme-dot theme-pink" title="粉色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('ocean')" class="theme-dot theme-ocean" title="海洋主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('wechat')" class="theme-dot theme-wechat" title="微信风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('alipay')" class="theme-dot theme-alipay" title="支付宝风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('simple')" class="theme-dot theme-simple" title="简约主题"></a>
            </div>
        </div>
        <div class="footer-info">
            <p>京ICP证060405号</p>
            <p>客户服务热线: 10105858</p>
        </div>
    </div>
    <script>
    // 主题切换功能
    function switchTheme(theme) {
        // 设置cookie，有效期30天
        var date = new Date();
        date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000));
        document.cookie = "site_theme=" + theme + "; expires=" + date.toUTCString() + "; path=/";
        
        // 更新页面上的主题类
        document.documentElement.className = document.documentElement.className.replace(/theme-\w+/g, '');
        document.documentElement.classList.add('theme-' + theme);
        
        // 同时更新body的主题类，确保背景色只应用于内容区域
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add('theme-' + theme);
        
        // 更新当前选中的主题点
        highlightCurrentTheme(theme);
        
        // 显示切换成功提示
        var themeNames = {
            'red': '红色',
            'blue': '蓝色',
            'green': '绿色',
            'purple': '紫色',
            'orange': '橙色',
            'pink': '粉色',
            'ocean': '海洋',
            'wechat': '微信风格',
            'alipay': '支付宝风格',
            'miui': '小米风格',
            'douyin': '抖音风格',
            'simple': '简约'
        };
        
        // 创建提示元素
        var toast = document.createElement('div');
        toast.className = 'theme-toast';
        toast.textContent = '已切换到' + themeNames[theme] + '主题';
        document.body.appendChild(toast);
        
        // 2秒后移除提示
        setTimeout(function() {
            toast.classList.add('hide');
            setTimeout(function() {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    // 高亮当前主题
    function highlightCurrentTheme(theme) {
        // 移除所有主题点的高亮
        var themeDots = document.querySelectorAll('.theme-dot');
        themeDots.forEach(function(dot) {
            dot.classList.remove('active');
        });
        
        // 添加当前主题的高亮
        var currentThemeDot = document.querySelector('.theme-dot.theme-' + theme);
        if (currentThemeDot) {
            currentThemeDot.classList.add('active');
        }
    }
    
    // 在页面加载时，根据当前主题高亮对应的主题点
    document.addEventListener('DOMContentLoaded', function() {
        // 获取当前主题
        var currentTheme = 'red'; // 默认主题
        var htmlClass = document.documentElement.className;
        var themeMatch = htmlClass.match(/theme-(\w+)/);
        
        if (themeMatch && themeMatch[1]) {
            currentTheme = themeMatch[1];
            
            // 确保body也具有相同的主题类
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            document.body.classList.add('theme-' + currentTheme);
        }
        
        // 高亮当前主题
        highlightCurrentTheme(currentTheme);
    });
    </script>
    <style>
    /* 主题切换样式 */
    html, body {
        min-height: 100%;
    }
    
    body {
        background-color: #f5f5f5; /* 默认背景色 */
        margin: 0;
        padding: 0;
    }
    
    /* 确保主题颜色只应用于body */
    html.theme-red, html.theme-blue, html.theme-green, 
    html.theme-purple, html.theme-orange, html.theme-pink,
    html.theme-ocean, html.theme-wechat, html.theme-alipay,
    html.theme-miui, html.theme-douyin {
        background-color: #fff; /* 重置HTML背景为白色 */
    }
    
    /* 主题背景色应用到body */
    body.theme-red { background-color: #fff5f5; }
    body.theme-blue { background-color: #f5f8ff; }
    body.theme-green { background-color: #f5fff8; }
    body.theme-purple { background-color: #f8f5ff; }
    body.theme-orange { background-color: #fff9f5; }
    body.theme-pink { background-color: #fff5f9; }
    body.theme-ocean { background-color: #f5faff; }
    body.theme-wechat { background-color: #f5fff7; }
    body.theme-alipay { background-color: #f5faff; }
    body.theme-simple { background-color: #f8f8f8; }
    
    .theme-switcher {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
    }
    
    .theme-switcher p {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }
    
    .theme-dots {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
        padding: 5px;
    }
    
    .theme-dot {
        display: inline-block;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .theme-dot:hover {
        transform: scale(1.2);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .theme-dot.active {
        transform: scale(1.2);
        box-shadow: 0 0 0 2px #fff, 0 0 0 4px var(--primary-color, currentColor);
    }
    
    /* 主题颜色 */
    .theme-red {
        background-color: #e53935;
    }
    
    .theme-blue {
        background-color: #4285f4;
    }
    
    .theme-green {
        background-color: #00a878;
    }
    
    .theme-purple {
        background-color: #7b68ee;
    }
    
    .theme-orange {
        background-color: #ff6b01;
    }
    
    .theme-pink {
        background-color: #e91e63;
    }
    
    .theme-ocean {
        background-color: #006994;
    }
    
    .theme-wechat {
        background-color: #07c160;
    }
    
    .theme-alipay {
        background-color: #1677ff;
    }
    
    .theme-simple {
        background-color: #ffffff;
        border: 1px solid #eeeeee;
    }
    
    /* 主题切换toast提示 */
    .theme-toast {
        position: fixed;
        bottom: 80px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 1000;
        opacity: 1;
        transition: opacity 0.3s;
    }
    
    .theme-toast.hide {
        opacity: 0;
    }
    
    /* 简约主题特殊处理 */
    .theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
    }
    
    .theme-simple .header-back,
    .theme-simple .header-title,
    .theme-simple .header-share,
    .theme-simple .header-search-icon {
        color: #333333 !important;
    }
    
    .theme-simple .header-back:active,
    .theme-simple .header-share:active,
    .theme-simple .header-search-icon:active {
        background-color: rgba(0,0,0,0.05) !important;
    }
    </style>
</footer>


    <script>
    // 处理后端数据，为模板准备数据
    // 兼容性加载处理，防止FOUC
    document.documentElement.classList.add('no-fouc');
    
    document.addEventListener('DOMContentLoaded', function() {
        // 确保页面已加载完成
        // 默认显示文本列表视图
        document.getElementById('tab-text').classList.add('active');
        
        // 预设关键元素高度
        document.querySelectorAll('.tab-content').forEach(function(content) {
            if (!content.style.minHeight) {
                content.style.minHeight = (window.innerHeight * 0.4) + 'px';
            }
        });
        
        // 移除FOUC类以显示内容
        setTimeout(function() {
            document.documentElement.classList.remove('no-fouc');
            document.body.classList.add('content-loaded');
        }, 50);

        // 加载搜索历史
        renderSearchHistory();

        // 点击外部关闭搜索层
        document.addEventListener('click', function(event) {
            const searchLayer = document.getElementById('searchLayer');
            if (searchLayer.classList.contains('active') && 
                !event.target.closest('.search-layer') && 
                !event.target.closest('.search-box')) {
                toggleSearch();
            }
        });
        
        // 处理滚动事件，显示/隐藏固定头部
        const stickyHeader = document.getElementById('stickyHeader');
        const mainHeaderHeight = document.querySelector('.main-header').offsetHeight;
        let lastScrollY = window.scrollY;
        let scrollTimer = null;
        let isHeaderVisible = false;

        function updateStickyHeader() {
            const currentScrollY = window.scrollY;

            // 只要滚动超过主头部高度就显示固定头部，无论向上还是向下滚动
            if (currentScrollY > mainHeaderHeight) {
                if (!isHeaderVisible) {
                    stickyHeader.classList.add('visible');
                    isHeaderVisible = true;
                }
            } else {
                // 滚动没有超过主头部高度，隐藏固定头部
                if (isHeaderVisible) {
                    stickyHeader.classList.remove('visible');
                    isHeaderVisible = false;
                }
            }

            lastScrollY = currentScrollY;
        }

        window.addEventListener('scroll', function() {
            // 防抖处理，避免频繁触发动画
            if (scrollTimer) {
                clearTimeout(scrollTimer);
            }

            scrollTimer = setTimeout(updateStickyHeader, 16); // 约60fps
        });
    });
    


    // 搜索层相关函数
    function toggleSearch() {
        const searchLayer = document.getElementById('searchLayer');
        if (searchLayer.classList.contains('active')) {
            searchLayer.style.transform = 'translateY(-10px)';
            searchLayer.style.opacity = '0';
            setTimeout(() => {
                searchLayer.classList.remove('active');
            }, 250);
        } else {
            searchLayer.classList.add('active');
            setTimeout(() => {
                searchLayer.style.transform = 'translateY(0)';
                searchLayer.style.opacity = '1';
                document.querySelector('.search-input').focus();
            }, 10);
        }
    }

    // 搜索标签点击
    function searchTag(element) {
        const keyword = element.innerText;
        addSearchHistory(keyword);
        showSearchLoading();
        window.location.href = '/search.php?keyword=' + encodeURIComponent(keyword);
    }

    // 显示搜索加载状态
    function showSearchLoading() {
        const searchForm = document.querySelector('.search-form');
        if (searchForm) {
            searchForm.classList.add('loading');
        }
    }

    // 隐藏搜索加载状态
    function hideSearchLoading() {
        const searchForm = document.querySelector('.search-form');
        if (searchForm) {
            searchForm.classList.remove('loading');
        }
    }

    // 获取搜索历史
    function getSearchHistory() {
        const history = localStorage.getItem('searchHistory');
        return history ? JSON.parse(history) : [];
    }

    // 添加搜索历史
    function addSearchHistory(keyword) {
        if (!keyword.trim()) return;
        let history = getSearchHistory();
        // 移除重复项
        history = history.filter(item => item !== keyword);
        // 添加到开头
        history.unshift(keyword);
        // 保留最新10条
        if (history.length > 10) {
            history = history.slice(0, 10);
        }
        localStorage.setItem('searchHistory', JSON.stringify(history));
        renderSearchHistory();
    }

    // 清空搜索历史
    function clearSearchHistory() {
        localStorage.removeItem('searchHistory');
        renderSearchHistory();
    }

    // 渲染搜索历史标签
    function renderSearchHistory() {
        const historyTags = document.getElementById('searchHistoryTags');
        const history = getSearchHistory();
        historyTags.innerHTML = '';
        
        if (history.length === 0) {
            historyTags.innerHTML = '<div style="color:#999;padding:10px 0;">暂无搜索历史</div>';
            return;
        }
        
        history.forEach(item => {
            const tag = document.createElement('a');
            tag.href = 'javascript:void(0);';
            tag.className = 'search-tag';
            tag.innerText = item;
            tag.onclick = function() {
                searchTag(this);
            };
            historyTags.appendChild(tag);
        });
    }

    // 页面加载时，如果有keyword参数，将其添加到历史记录
    document.addEventListener('DOMContentLoaded', function() {
        renderSearchHistory();

        const urlParams = new URLSearchParams(window.location.search);
        const keyword = urlParams.get('keyword');
        if (keyword) {
            addSearchHistory(keyword);
        }

        // 绑定首页搜索表单提交事件
        const homeSearchForm = document.getElementById('home-search-form');
        if (homeSearchForm) {
            homeSearchForm.addEventListener('submit', function(e) {
                const input = document.getElementById('home-search-input');
                const keyword = input.value.trim();

                if (keyword) {
                    addSearchHistory(keyword);
                    showSearchLoading();
                }
            });
        }
    });
    </script>
</body>
</html> 