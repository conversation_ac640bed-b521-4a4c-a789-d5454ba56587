<?php
// 定义安全常量
define('IN_BTMPS', true);

/**
 * 内容块管理页面
 */
require_once('../include/common.inc.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 引入后台公共函数
require_once(dirname(__FILE__) . '/include/admin.fun.php');

// 获取操作类型
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : $action;
    // 在POST请求中也要获取ID（用于编辑）
    if (isset($_POST['id']) && intval($_POST['id']) > 0) {
        $id = intval($_POST['id']);
    }
}

// 消息变量
$message = '';
$message_type = '';

// 根据操作类型处理
switch ($action) {
    case 'add':
    case 'edit':
        // 处理添加/编辑表单提交
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = trim($_POST['name'] ?? '');
            $identifier = trim($_POST['identifier'] ?? '');
            $content = $_POST['content'] ?? '';
            $description = trim($_POST['description'] ?? '');
            $status = intval($_POST['status'] ?? 1);
            $sort_order = intval($_POST['sort_order'] ?? 0);
            
            // 先保留表单数据，以防验证失败时需要重新显示
            $form_data = array(
                'id' => $id,
                'name' => $name,
                'identifier' => $identifier,
                'content' => $content,
                'description' => $description,
                'status' => $status,
                'sort_order' => $sort_order
            );

            // 验证必填字段
            if (empty($name)) {
                $message = '块名称不能为空';
                $message_type = 'error';
                $block_data = $form_data;
            } elseif (empty($identifier)) {
                $message = '调用标识符不能为空';
                $message_type = 'error';
                $block_data = $form_data;
            } elseif (!preg_match('/^[a-zA-Z0-9_-]+$/', $identifier)) {
                $message = '调用标识符只能包含字母、数字、下划线和短横线';
                $message_type = 'error';
                $block_data = $form_data;
            } elseif (empty($content)) {
                $message = 'HTML内容不能为空';
                $message_type = 'error';
                $block_data = $form_data;
            } else {
                // 检查标识符是否重复
                $check_sql = "SELECT id FROM content_blocks WHERE identifier = ?";
                $check_params = [$identifier];

                if ($action === 'edit' && $id > 0) {
                    $check_sql .= " AND id != ?";
                    $check_params[] = $id;
                }

                $check_result = $db->query($check_sql, $check_params);

                if ($check_result && $db->fetch_array($check_result)) {
                    $message = '调用标识符已存在，请使用其他标识符';
                    $message_type = 'error';
                    $block_data = $form_data;
                } else {
                    $current_time = time();
                    
                    if ($action === 'edit' && $id > 0) {
                        // 更新
                        $sql = "UPDATE content_blocks SET name = ?, identifier = ?, content = ?, description = ?, status = ?, sort_order = ?, updated_at = ? WHERE id = ?";
                        $params = [$name, $identifier, $content, $description, $status, $sort_order, $current_time, $id];
                        $result = $db->query($sql, $params);
                        
                        if ($result) {
                            // 清除该块的缓存
                            blocks_cache_delete($identifier);
                            $message = '内容块更新成功';
                            $message_type = 'success';
                        } else {
                            $message = '内容块更新失败：' . $db->error();
                            $message_type = 'error';
                        }
                    } else {
                        // 添加
                        $sql = "INSERT INTO content_blocks (name, identifier, content, description, status, sort_order, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                        $params = [$name, $identifier, $content, $description, $status, $sort_order, $current_time, $current_time];
                        $result = $db->query($sql, $params);
                        
                        if ($result) {
                            $message = '内容块添加成功';
                            $message_type = 'success';
                        } else {
                            $message = '内容块添加失败：' . $db->error();
                            $message_type = 'error';
                        }
                    }
                }
            }
        }
        
        // 获取编辑数据
        $block_data = null;
        if ($action === 'edit' && $id > 0) {
            // 如果是POST请求且有错误，$block_data已经在上面的验证逻辑中设置了
            // 如果是GET请求或POST请求成功，从数据库获取最新数据
            if (!isset($block_data) && ($_SERVER['REQUEST_METHOD'] !== 'POST' || empty($message) || $message_type === 'success')) {
                $sql = "SELECT * FROM content_blocks WHERE id = ?";
                $result = $db->query($sql, [$id]);
                if ($result) {
                    $block_data = $db->fetch_array($result);
                    if (!$block_data) {
                        $message = '内容块不存在';
                        $message_type = 'error';
                        $action = 'list';
                    }
                }
            }
        }
        break;
        
    case 'delete':
        if ($id > 0) {
            // 获取块信息用于清除缓存
            $sql = "SELECT identifier FROM content_blocks WHERE id = ?";
            $result = $db->query($sql, [$id]);
            $block_info = $db->fetch_array($result);
            
            // 删除记录
            $sql = "DELETE FROM content_blocks WHERE id = ?";
            $result = $db->query($sql, [$id]);
            
            if ($result) {
                // 清除缓存
                if ($block_info) {
                    blocks_cache_delete($block_info['identifier']);
                }
                $message = '内容块删除成功';
                $message_type = 'success';
            } else {
                $message = '内容块删除失败：' . $db->error();
                $message_type = 'error';
            }
        }
        $action = 'list';
        break;
        
    case 'toggle_status':
        if ($id > 0) {
            // 获取当前状态
            $sql = "SELECT status, identifier FROM content_blocks WHERE id = ?";
            $result = $db->query($sql, [$id]);
            $block_info = $db->fetch_array($result);
            
            if ($block_info) {
                $new_status = $block_info['status'] ? 0 : 1;
                $sql = "UPDATE content_blocks SET status = ?, updated_at = ? WHERE id = ?";
                $result = $db->query($sql, [$new_status, time(), $id]);
                
                if ($result) {
                    // 清除缓存
                    blocks_cache_delete($block_info['identifier']);
                    $status_text = $new_status ? '启用' : '禁用';
                    $message = "内容块{$status_text}成功";
                    $message_type = 'success';
                } else {
                    $message = '状态更新失败：' . $db->error();
                    $message_type = 'error';
                }
            }
        }
        $action = 'list';
        break;
        
    case 'clear_cache':
        $count = blocks_cache_clear_all();
        $message = "已清除 {$count} 个块缓存文件";
        $message_type = 'success';
        $action = 'list';
        break;
        
    case 'warmup_cache':
        $count = blocks_cache_warmup();
        $message = "已预热 {$count} 个块缓存";
        $message_type = 'success';
        $action = 'list';
        break;
}

// 获取列表数据
if ($action === 'list') {
    $sql = "SELECT * FROM content_blocks ORDER BY sort_order ASC, id DESC";
    $result = $db->query($sql);
    $blocks = array();
    while ($row = $db->fetch_array($result)) {
        // 预处理时间格式，确保时间戳是整数类型
        $row['created_at'] = intval($row['created_at']);
        $row['updated_at'] = intval($row['updated_at']);

        // 添加格式化的时间字符串
        $row['created_date'] = date('Y-m-d', $row['created_at']);
        $row['created_time'] = date('H:i', $row['created_at']);

        $blocks[] = $row;
    }
}

// 设置模板目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 设置公共模板变量
set_admin_template_vars($tpl, 'content_blocks', '内容块管理');

// 分配变量到模板
$tpl->assign('action', $action);
$tpl->assign('message', $message);
$tpl->assign('message_type', $message_type);

if ($action === 'list') {
    $tpl->assign('blocks', $blocks);
    // 获取缓存统计信息
    $cache_stats = blocks_cache_stats();
    $tpl->assign('cache_stats', $cache_stats);
} elseif (in_array($action, ['add', 'edit'])) {
    $tpl->assign('block_data', $block_data);
    $tpl->assign('id', $id);
}

// 显示模板
$tpl->display('content_blocks.htm');
?>
