<?php
if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

/**
 * 验证码生成文件
 * 兼容PHP 8.2，支持自定义配置
 */

// 清理输出缓冲区
if (ob_get_level()) {
    ob_clean();
}

// 定义安全常量
// 设置错误处理 - 不显示错误到输出
error_reporting(0);
ini_set('display_errors', 0);

// 验证码配置
$captcha_config = array(
    // 图片尺寸 - 增大尺寸以容纳更大的字体
    'width' => 140,           // 图片宽度
    'height' => 60,           // 图片高度

    // 验证码设置
    'codeLength' => 4,        // 验证码长度
    'chars' => 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789', // 字符集（使用大写字母，更清晰）
    'sessionKey' => 'verify_code', // Session存储键名

    // 字体设置
    'fontSize' => 20,         // 字体大小
    'fontFile' => null,       // 字体文件路径（使用内置字体）

    // 干扰设置
    'noiseLines' => 2,        // 干扰线数量
    'noisePixels' => 15,      // 干扰点数量（减少以提高可读性）
);

// 启动会话
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 不包含复杂的公共文件，避免输出干扰

// 检查GD库支持
if (!extension_loaded('gd')) {
    // 输出错误图片
    header('Content-Type: image/png');
    // 输出一个1x1的透明PNG
    echo base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    exit;
}

// 检查图像函数支持
if (!function_exists('imagecreatetruecolor')) {
    header('Content-Type: image/png');
    echo base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    exit;
}

try {
    // 直接生成验证码，不依赖复杂的类
    generateSimpleCaptcha($captcha_config);
} catch (Exception $e) {
    // 错误处理 - 输出简单的错误图片
    generateErrorImage($e->getMessage());
} catch (Error $e) {
    // PHP 8.2 错误处理
    generateErrorImage($e->getMessage());
}

/**
 * 生成简单的验证码图片
 */
function generateSimpleCaptcha($config) {
    // 创建图像
    $width = $config['width'];
    $height = $config['height'];
    $image = imagecreatetruecolor($width, $height);

    if (!$image) {
        throw new Exception('无法创建图像资源');
    }

    // 设置背景色
    $bg_color = imagecolorallocate($image, 245, 245, 250);
    if ($bg_color === false) {
        imagedestroy($image);
        throw new Exception('无法分配背景颜色');
    }
    imagefill($image, 0, 0, $bg_color);

    // 生成验证码
    $chars = $config['chars'];
    $code = '';
    $chars_len = strlen($chars) - 1;

    for ($i = 0; $i < $config['codeLength']; $i++) {
        $code .= $chars[mt_rand(0, $chars_len)];
    }

    // 存储到Session
    $_SESSION[$config['sessionKey']] = strtolower($code);

    // 绘制文字 - 使用内置字体，增大字体
    $char_width = ($width - 20) / $config['codeLength']; // 留出边距
    for ($i = 0; $i < $config['codeLength']; $i++) {
        $text_color = imagecolorallocate($image, mt_rand(20, 60), mt_rand(20, 60), mt_rand(20, 60));
        if ($text_color === false) {
            $text_color = imagecolorallocate($image, 40, 40, 40);
        }

        // 计算字符位置，确保居中且有适当间距
        $x = (int)(10 + $char_width * $i + mt_rand(5, 12));
        $y = (int)(($height - 15) / 2 + mt_rand(-4, 4)); // 垂直居中

        // 使用最大的内置字体 (5)，并绘制多次以增加粗细效果
        imagestring($image, 5, $x, $y, $code[$i], $text_color);

        // 绘制阴影效果，让字体看起来更粗更清晰
        $shadow_color = imagecolorallocate($image, mt_rand(60, 100), mt_rand(60, 100), mt_rand(60, 100));
        if ($shadow_color !== false) {
            imagestring($image, 5, $x + 1, $y, $code[$i], $shadow_color);
            imagestring($image, 5, $x, $y + 1, $code[$i], $shadow_color);
            imagestring($image, 5, $x + 1, $y + 1, $code[$i], $shadow_color);
        }

        // 再次绘制主字符，确保清晰
        imagestring($image, 5, $x, $y, $code[$i], $text_color);
    }

    // 添加干扰线
    for ($i = 0; $i < $config['noiseLines']; $i++) {
        $line_color = imagecolorallocate($image, mt_rand(180, 220), mt_rand(180, 220), mt_rand(180, 220));
        if ($line_color !== false) {
            imageline($image,
                mt_rand(0, $width), mt_rand(0, $height),
                mt_rand(0, $width), mt_rand(0, $height),
                $line_color
            );
        }
    }

    // 添加干扰点
    for ($i = 0; $i < $config['noisePixels']; $i++) {
        $pixel_color = imagecolorallocate($image, mt_rand(180, 220), mt_rand(180, 220), mt_rand(180, 220));
        if ($pixel_color !== false) {
            imagesetpixel($image, mt_rand(0, $width), mt_rand(0, $height), $pixel_color);
        }
    }

    // 输出图片
    header('Content-Type: image/png');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');

    imagepng($image);
    imagedestroy($image);
}

/**
 * 生成错误图片
 */
function generateErrorImage($message) {
    $width = 140;
    $height = 60;
    $image = imagecreatetruecolor($width, $height);

    if ($image) {
        $bg_color = imagecolorallocate($image, 255, 255, 255);
        $text_color = imagecolorallocate($image, 255, 0, 0);

        if ($bg_color !== false && $text_color !== false) {
            imagefill($image, 0, 0, $bg_color);
            imagestring($image, 3, 10, 15, 'ERROR', $text_color);
            imagestring($image, 2, 10, 30, substr($message, 0, 15), $text_color);

            header('Content-Type: image/png');
            imagepng($image);
            imagedestroy($image);
            return;
        }
    }

    // 如果无法创建错误图片，输出透明PNG
    header('Content-Type: image/png');
    echo base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
}