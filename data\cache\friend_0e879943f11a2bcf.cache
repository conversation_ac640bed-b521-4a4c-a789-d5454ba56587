<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1755856778,
  'data' => 
  array (
    0 => 
    array (
      'id' => '3',
      'name' => '阿里巴巴',
      'url' => 'https://www.alibaba.com',
      'description' => '全球领先的小企业电子商务公司',
      'logo' => 'https://www.alibaba.com/favicon.ico',
      'sort_order' => '0',
      'target' => '_blank',
    ),
    1 => 
    array (
      'id' => '9',
      'name' => '底部导航',
      'url' => 'https://www.alibaba.com',
      'description' => '',
      'logo' => NULL,
      'sort_order' => '0',
      'target' => '_blank',
    ),
    2 => 
    array (
      'id' => '2',
      'name' => '腾讯',
      'url' => 'https://www.qq.com',
      'description' => '中国领先的互联网增值服务提供商',
      'logo' => 'https://www.qq.com/favicon.ico',
      'sort_order' => '2',
      'target' => '_blank',
    ),
    3 => 
    array (
      'id' => '6',
      'name' => '飒飒的2',
      'url' => 'http://www.fenlei.com/',
      'description' => '',
      'logo' => NULL,
      'sort_order' => '2',
      'target' => '_blank',
    ),
  ),
);
