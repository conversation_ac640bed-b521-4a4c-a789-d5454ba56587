<?php
/**
 * 分类信息网站公共函数和初始化文件
 * 所有页面都应该首先引入此文件
 */

// 开始计时
$__startTime = microtime(true);

// 设置错误报告级别
error_reporting(E_ALL);
ini_set('display_errors', 'On');

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义常量
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('INCLUDE_PATH', ROOT_PATH.'include/');
define('TEMPLATE_PATH', ROOT_PATH.'template/');
define('DATA_PATH', ROOT_PATH.'data/');
define('UPLOAD_PATH', ROOT_PATH.'uploads/');
define('COMPILED_PATH', DATA_PATH.'compiled/');

// 确保编译目录存在
if (!is_dir(COMPILED_PATH)) {
    mkdir(COMPILED_PATH, 0777, true);
}

// 启动会话
session_start();

// 设置默认字符集
header('Content-Type: text/html; charset=utf-8');

// 加载数据库配置
require_once(ROOT_PATH.'config/config.db.php');

// 加载系统配置
require_once(ROOT_PATH.'config/config.inc.php');

// 加载模板引擎类
require_once(INCLUDE_PATH.'template.class.php');

// 初始化模板引擎
$tpl = new Template();

// 网站状态检查 - 尽早检查，在加载其他功能前
// 获取后台目录配置
$admin_path = isset($config['admin_path']) ? $config['admin_path'] : 'admin';

// 检查当前访问的是否是后台路径
$current_path = $_SERVER['SCRIPT_NAME'];
$is_admin_path = (strpos($current_path, '/' . $admin_path . '/') !== false);

// 只在前台页面检查网站状态
if (!$is_admin_path && $config['site_status'] == 0) {
    // 检测是否为移动设备访问
    $is_mobile = false;
    if (isset($_SERVER['HTTP_USER_AGENT'])) {
        $user_agent = strtolower($_SERVER['HTTP_USER_AGENT']);
        $is_mobile = (strpos($user_agent, 'mobile') !== false || 
                      strpos($user_agent, 'android') !== false || 
                      strpos($user_agent, 'iphone') !== false);
    }
    
    // 检测是否为移动端域名
    $is_mobile_domain = false;
    if (isset($_SERVER['HTTP_HOST'])) {
        $http_host = $_SERVER['HTTP_HOST'];
        $is_mobile_domain = (strpos($http_host, 'm.') === 0);
    }
    
    // 确定使用哪个模板目录
    $template_dir = ($is_mobile || $is_mobile_domain) ? 'm' : 'pc';
    
    // 检查对应模板目录下是否有closed.htm文件
    $template_file = TEMPLATE_PATH . $template_dir . '/closed.htm';
    if (!file_exists($template_file)) {
        // 如果没有找到对应模板，使用PC模板作为后备
        $template_dir = 'pc';
    }
    
    // 设置模板目录
    $tpl->setTemplateDir(TEMPLATE_PATH . $template_dir . '/');
    
    // 分配变量到模板
    $tpl->assign('close_reason', $config['site_close_reason']);
    $tpl->assign('site_name', $config['site_name']);
    
    // 显示关闭页面并终止脚本执行
    $tpl->display('closed.htm');
    exit;
}

// 加载数据库连接类
require_once(INCLUDE_PATH.'mysql.class.php');

// 加载验证码类
require_once(INCLUDE_PATH.'captcha.class.php');

/**
 * 数据库连接初始化
 * 使用配置文件中的参数连接数据库
 */
$db = new MySQL($config);

// 加载全局函数库
require_once(INCLUDE_PATH.'global.fun.php');

// 加载图片处理函数库
require_once(INCLUDE_PATH.'image.fun.php');

// 加载信息页面函数库
require_once(INCLUDE_PATH.'info.fun.php');

// 加载缓存类
require_once(INCLUDE_PATH.'cache.class.php');

// 加载新闻缓存类
require_once(INCLUDE_PATH.'news_cache.class.php');

// 加载内容块函数库
require_once(INCLUDE_PATH.'blocks.fun.php');

// 创建一个简单的对象替代缓存类，只处理必要方法
$cache = new stdClass();
$cache->getCategories = function() {
    return getCategories();
};
$cache->getRegions = function() {
    return getRegions();
};
$cache->exists = function() { return false; };
$cache->read = function() { return null; };
$cache->write = function() { return true; };
$cache->delete = function() { return true; };
$cache->getInfo = function() { 
    return [
        'count' => 0,
        'total_size' => 0,
        'total_size_formatted' => '0 B',
        'files' => []
    ]; 
};
$cache->refreshCategories = function() { return getCategories(); };
$cache->refreshRegions = function() { return getRegions(); };
$cache->getCategoryDetail = function() { return null; };
$cache->clearTopPostCache = function() { return true; };

// 初始化模板变量数组 - 用于临时存储模板变量，等待模板引擎初始化后再分配
$__template_vars = array();

// 从配置文件加载设置，不再从数据库重复读取
$settings = $config;

// 确保核心设置存在
if (!isset($settings['site_name'])) {
    $settings['site_name'] = '分类信息网';
}

// 如果自动模板检测设置不存在，设置默认值为开启
if (!isset($settings['auto_template_detection'])) {
    $settings['auto_template_detection'] = '1'; // 1-开启，0-关闭
}

// 将设置加入全局变量，便于在其他文件中访问
$GLOBALS['settings'] = $settings;

// 使用缓存获取导航菜单
$navList = getCachedNavigation();

// 临时存储导航数据，等待模板引擎初始化后再分配
$__template_vars['navList'] = $navList;

// 检测访问终端并设置模板目录
$templateDir = 'pc'; // 默认使用PC模板

// 判断是否开启自动模板检测
$autoTemplateDetection = isset($settings['auto_template_detection']) ? (bool)$settings['auto_template_detection'] : true;

// 获取指定的强制模板（如果有）
$forceTemplate = isset($_GET['template']) ? filter($_GET['template']) : '';

// 如果URL参数中指定了模板，则优先使用
if (!empty($forceTemplate) && in_array($forceTemplate, array('pc', 'm', 'wx', 'app'))) {
    $templateDir = $forceTemplate;
    // 将模板选择保存到Cookie，有效期1天
    setcookie('force_template', $forceTemplate, time() + 86400, '/');
} 
// 如果Cookie中存在强制模板选择，则使用Cookie中的值
else if (isset($_COOKIE['force_template']) && in_array($_COOKIE['force_template'], array('pc', 'm', 'wx', 'app'))) {
    $templateDir = $_COOKIE['force_template'];
}
// 否则，根据自动检测设置决定是否进行模板自动检测
else if ($autoTemplateDetection) {
    // 判断是否为命令行模式
    if (php_sapi_name() === 'cli') {
        // 命令行模式不需要检测用户代理
        $templateDir = 'pc';
    } else {
        // 网页模式下检测用户代理
        $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
        $httpHost = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
        $templateDir = 'pc';
        // 优先根据域名前缀判断模板
        if (strpos($httpHost, 'm.') === 0) {
            // 手机站域名
            $templateDir = 'm';
        } elseif (strpos($httpHost, 'wx.') === 0) {
            // 微信站域名
            $templateDir = 'wx';
        } elseif (strpos($httpHost, 'app.') === 0) {
            // APP站域名
            $templateDir = 'app';
        } elseif (strpos($httpHost, 'www.') === 0) {
            // PC站域名(www)
            $templateDir = 'pc';
        } elseif ($httpHost === 'localhost' || preg_match('/^\d+\.\d+\.\d+\.\d+$/', $httpHost)) {
            // 本地测试域名或IP访问，根据User-Agent检测设备类型
            if (strpos($userAgent, 'mobile') !== false || strpos($userAgent, 'android') !== false || strpos($userAgent, 'iphone') !== false) {
                $templateDir = 'm';
            } else {
                $templateDir = 'pc';
            }
        } elseif (strpos($userAgent, 'mobile') !== false || strpos($userAgent, 'android') !== false || strpos($userAgent, 'iphone') !== false) {
            // 检测移动设备(仅当域名没有明确指示终端类型时才使用)
            $templateDir = 'm';
        }
    }
}

// 记录当前使用的模板目录，以便其他地方使用
$GLOBALS['current_template_dir'] = $templateDir;

define('TEMPLATE_DIR', $templateDir);

// 将设置应用为常量，方便访问
foreach ($settings as $key => $value) {
    if (!defined(strtoupper($key))) {
        define(strtoupper($key), $value);
    }
}

// 重新设置模板目录（如果网站没有关闭）
$tpl->setTemplateDir(TEMPLATE_PATH . TEMPLATE_DIR . '/');

// 设置默认变量
$tpl->assign('site_name', isset($settings['site_name']) ? $settings['site_name'] : '分类信息网');

// 设置一些常用的模板变量
$__template_vars['site_name'] = isset($settings['site_name']) ? $settings['site_name'] : '分类信息网站';
$__template_vars['site_title'] = isset($settings['site_title']) ? $settings['site_title'] : '分类信息网站 - 免费发布信息平台';
$__template_vars['site_keywords'] = isset($settings['site_keywords']) ? $settings['site_keywords'] : '分类信息,免费发布,信息平台';
$__template_vars['site_description'] = isset($settings['site_description']) ? $settings['site_description'] : '分类信息网站是一个免费发布信息的平台，提供房产、招聘、二手交易等各类信息服务。';
$__template_vars['site_copyright'] = isset($settings['site_copyright']) ? $settings['site_copyright'] : 'Copyright © 2024 分类信息网站 All Rights Reserved';
$__template_vars['site_icp'] = isset($settings['site_icp']) ? $settings['site_icp'] : '';
$__template_vars['site_analytics'] = isset($settings['site_analytics']) ? $settings['site_analytics'] : '';
$__template_vars['site_status'] = isset($settings['site_status']) ? $settings['site_status'] : '1';
$__template_vars['site_close_reason'] = isset($settings['site_close_reason']) ? $settings['site_close_reason'] : '网站维护中，请稍后再访问...';

// 确定站点URL
if (php_sapi_name() === 'cli') {
    $__template_vars['site_url'] = isset($settings['site_url']) ? $settings['site_url'] : 'http://www.example.com';
} else {
    // 自动检测协议(HTTP/HTTPS)
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $__template_vars['site_url'] = $protocol . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost');
}

$__template_vars['template_dir'] = TEMPLATE_DIR;

// 将临时存储的模板变量一次性分配给模板引擎
foreach ($__template_vars as $key => $value) {
    $tpl->assign($key, $value);
}

// 预加载常用缓存数据
// 从缓存获取分类数据
// $categories = getCachedCategories();
// $GLOBALS['cached_categories'] = $categories;

// 从缓存获取区域数据
// $regions = getCachedRegions();
// $GLOBALS['cached_regions'] = $regions;

// 使用缓存获取分类数据
$categories = getCachedCategories();
$GLOBALS['cached_categories'] = $categories;

// 使用缓存获取区域数据（从area表）
$areas = getCachedAreas();
$GLOBALS['cached_areas'] = $areas;
// 向后兼容：保持 cached_regions 变量
$GLOBALS['cached_regions'] = $areas;

// 加载扩展函数库
if (file_exists(INCLUDE_PATH . 'functions.inc.php')) {
    include_once(INCLUDE_PATH . 'functions.inc.php');
}

// 防SQL注入和XSS攻击处理
if (!empty($_GET)) {
    $_GET = array_map('filter', $_GET);
}

if (!empty($_POST)) {
    // UEditor富文本内容字段，不进行HTML编码
    $ueditor_fields = array('content', 'description');

    foreach ($_POST as $key => $value) {
        if (in_array($key, $ueditor_fields)) {
            // UEditor内容只进行基本的trim处理，不进行HTML编码
            $_POST[$key] = is_string($value) ? trim($value) : $value;
        } else {
            // 其他字段正常过滤
            $_POST[$key] = filter($value);
        }
    }
}

// 执行到这里意味着初始化完成
// 正式页面代码将接着执行



