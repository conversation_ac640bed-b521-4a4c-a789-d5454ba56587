<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>发布信息 - <?php echo $site_name; ?></title>
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css">
    <link rel="stylesheet" href="/template/m/css/common.css">
    <link rel="stylesheet" href="/template/m/css/post.css?v=20250801">
    <link rel="stylesheet" href="/static/css/image-compress.css">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/mobile-image-compress.js"></script>
    <script>
    // 简化的手机端图片压缩功能
    function SimpleMobileCompressor() {
        this.compressFile = function(file, progressCallback) {
            return new Promise((resolve, reject) => {
                if (!file.type.match('image.*')) {
                    reject(new Error('不支持的文件类型'));
                    return;
                }

                if (progressCallback) progressCallback(10, '正在读取图片...');

                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        try {
                            if (progressCallback) progressCallback(50, '正在压缩图片...');

                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 手机端使用更小的尺寸
                            let { width, height } = img;
                            const maxWidth = 1200;
                            const maxHeight = 1200;

                            if (width > maxWidth || height > maxHeight) {
                                const ratio = Math.min(maxWidth / width, maxHeight / height);
                                width *= ratio;
                                height *= ratio;
                            }

                            canvas.width = width;
                            canvas.height = height;

                            // 绘制图片
                            ctx.drawImage(img, 0, 0, width, height);

                            if (progressCallback) progressCallback(90, '正在生成文件...');

                            // 转换为Blob
                            canvas.toBlob(function(blob) {
                                const compressedFile = new File([blob], file.name, {
                                    type: 'image/jpeg',
                                    lastModified: Date.now()
                                });

                                if (progressCallback) progressCallback(100, '压缩完成');

                                resolve({
                                    file: compressedFile,
                                    compressed: true,
                                    originalSize: file.size,
                                    finalSize: blob.size,
                                    compressionRatio: ((1 - blob.size / file.size) * 100).toFixed(1) + '%',
                                    dimensions: { width: Math.round(width), height: Math.round(height) }
                                });
                            }, 'image/jpeg', 0.7);

                        } catch (error) {
                            reject(error);
                        }
                    };
                    img.onerror = () => reject(new Error('图片加载失败'));
                    img.src = e.target.result;
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsDataURL(file);
            });
        };

        this.showMobileProgress = function(container, show) {
            // 简化的进度显示
            if (show) {
                if (!document.getElementById('simple-mobile-progress')) {
                    const progressEl = document.createElement('div');
                    progressEl.id = 'simple-mobile-progress';
                    progressEl.style.cssText = `
                        position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                        background: rgba(0,0,0,0.8); color: white; padding: 20px;
                        border-radius: 8px; z-index: 9999; text-align: center;
                    `;
                    progressEl.innerHTML = '<div>正在处理图片...</div>';
                    document.body.appendChild(progressEl);
                }
            } else {
                const progressEl = document.getElementById('simple-mobile-progress');
                if (progressEl) progressEl.remove();
            }
        };

        this.updateMobileProgress = function(percent, text) {
            const progressEl = document.getElementById('simple-mobile-progress');
            if (progressEl && text) {
                progressEl.innerHTML = `<div>${text}</div>`;
            }
        };
    }
    </script>
    <style>
        /* 确保简约主题头部为白色背景 */
        .theme-simple header {
            background-color: #ffffff !important;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">发布信息</div>
            <div class="header-right">
                <!-- 右侧可以为空 -->
            </div>
        </div>
    </header>

    <div class="post-content">
        <form action="/app.php?m=post<?php echo isset($category) && !empty($category) ? '&category_id='.$category['id'] : ''; ?>" method="post" enctype="multipart/form-data">
            <?php if (isset($category) && !empty($category)): ?>
            <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
            <?php endif; ?>
            <!-- 添加CSRF令牌和AJAX标识 -->
            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
            <input type="hidden" name="ajax" value="1">

            <!-- 隐藏面包屑导航，使界面更简洁 -->
            <!--
            <div class="breadcrumb">
                <div class="container">
                    <a href="/">首页</a>
                    <span class="separator"></span>
                    <a href="/app.php?m=post">选择栏目</a>
                    <?php if (isset($category) && !empty($category)): ?>
                    <span class="separator"></span>
                    <a href="/<?php echo $category['pinyin']; ?>/"><?php echo $category['name']; ?></a>
                    <?php endif; ?>
                </div>
            </div>
            -->

            <?php if (!empty($error_message)): ?>
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i><?php echo $error_message; ?>
            </div>
            <?php endif; ?>

            <div class="form-panel">
                <!-- 选择分类栏目 -->
                <div class="form-group required form-arrow" style="padding: 16px 10px;">
                    <label for="category_id" class="form-label">选择分类</label>
                    <div class="form-right">
                        <?php if (!isset($category) || empty($category)): ?>
                        <select name="category_id" id="category_id" class="form-control form-select" required>
                            <option value="">请选择分类</option>
                            <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo $cat['id']; ?>"><?php echo $cat['name']; ?></option>
                            <?php endforeach; ?>
                        </select>
                        <?php else: ?>
                        <div class="selected-category">
                            <strong><?php echo $category['name']; ?></strong>
                            <a href="/app.php?m=post" class="reselect-category">[重选]</a>
                        </div>
                        <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- 选择地区 -->
                <div class="form-group required form-arrow">
                    <label for="region_id" class="form-label">所在地区</label>
                    <div class="form-right">
                        <select name="region_id" id="region_id" class="form-control form-select" required>
                            <option value="">请选择地区</option>
                            <?php foreach ($regions as $province): ?>
                            <optgroup label="<?php echo $province['name']; ?>">
                                <?php foreach ($province['children'] as $city): ?>
                                <option value="<?php echo $city['id']; ?>" <?php echo (isset($post['region_id']) && $post['region_id'] == $city['id']) ? 'selected' : ''; ?>><?php echo $city['name']; ?></option>
                                <?php endforeach; ?>
                            </optgroup>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <!-- 信息有效期 -->
                <div class="form-group required form-arrow">
                    <label for="expire_days" class="form-label">有效期</label>
                    <div class="form-right">
                        <select name="expire_days" id="expire_days" class="form-control form-select" required>
                            <option value="7">7天</option>
                            <option value="15">15天</option>
                            <option value="30" selected>30天</option>
                            <option value="60">60天</option>
                            <option value="90">90天</option>
                        </select>
                    </div>
                </div>
                
                <!-- 标题 -->
                <div class="form-group required">
                    <label for="title" class="form-label">标题</label>
                    <div class="form-right">
                        <input type="text" name="title" id="title" class="form-control" placeholder="请输入标题" value="<?php echo isset($post['title']) ? $post['title'] : ''; ?>" required>
                    </div>
                </div>
                
                <!-- 内容 -->
                <div class="form-group form-vertical required">
                    <label for="content" class="form-label">详细内容</label>
                    <div class="form-right">
                        <textarea name="content" id="content" class="form-control form-textarea" placeholder="请输入详细内容" required><?php echo isset($post['content']) ? $post['content'] : ''; ?></textarea>
                    </div>
                </div>

                <!-- 图片上传 -->
                <div class="form-group form-vertical">
                    <label class="form-label">上传图片</label>
                    <div class="form-right">
                        <div class="image-upload">
                            <input type="file" id="image_uploads" name="images[]" accept="image/*" multiple class="hidden-input">
                            <label for="image_uploads" class="upload-btn">
                                <i class="fas fa-plus"></i>
                                <span style="margin-top:5px;font-size:12px;">添加图片</span>
                            </label>
                            <span class="form-hint image-hint">支持JPG、PNG格式，最多{$upload_config.max_count}张</span>
                            <div class="image-previews" id="image-previews"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($custom_fields)): ?>
            <div class="form-panel custom-fields" id="custom-fields">
                <?php foreach ($custom_fields as $field): ?>
                <div class="form-group <?php echo $field['required'] ? 'required' : ''; ?> <?php echo ($field['type'] == 'select') ? 'form-arrow' : ''; ?>">
                    <label for="field_<?php echo $field['id']; ?>" class="form-label">
                        <?php echo $field['label']; ?>
                    </label>
                    <div class="form-right">
                    <?php if ($field['type'] == 'text'): ?>
                    <input type="text" name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>" 
                           class="form-control" placeholder="请输入<?php echo $field['label']; ?>" <?php echo $field['required'] ? 'required' : ''; ?>>
                    
                    <?php elseif ($field['type'] == 'textarea'): ?>
                    <textarea name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>" 
                              class="form-control form-textarea" placeholder="请输入<?php echo $field['label']; ?>" <?php echo $field['required'] ? 'required' : ''; ?>></textarea>
                    
                    <?php elseif ($field['type'] == 'select'): ?>
                    <select name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>" 
                            class="form-control form-select" <?php echo $field['required'] ? 'required' : ''; ?>>
                        <option value="">请选择</option>
                        <?php foreach (explode(',', $field['options']) as $option): ?>
                        <option value="<?php echo $option; ?>"><?php echo $option; ?></option>
                        <?php endforeach; ?>
                    </select>
                    
                    <?php elseif ($field['type'] == 'radio'): ?>
                    <div class="radio-group">
                        <?php foreach (explode(',', $field['options']) as $key => $option): ?>
                        <div class="radio-item">
                            <input type="radio" name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>_<?php echo $key; ?>" 
                                   value="<?php echo $option; ?>" <?php echo $key == 0 && $field['required'] ? 'required' : ''; ?>>
                            <label for="field_<?php echo $field['id']; ?>_<?php echo $key; ?>"><?php echo $option; ?></label>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <?php elseif ($field['type'] == 'checkbox'): ?>
                    <div class="checkbox-group">
                        <?php foreach (explode(',', $field['options']) as $key => $option): ?>
                        <div class="checkbox-item">
                            <input type="checkbox" name="fields[<?php echo $field['id']; ?>][]" id="field_<?php echo $field['id']; ?>_<?php echo $key; ?>" 
                                   value="<?php echo $option; ?>">
                            <label for="field_<?php echo $field['id']; ?>_<?php echo $key; ?>"><?php echo $option; ?></label>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <?php elseif ($field['type'] == 'number'): ?>
                    <input type="number" name="fields[<?php echo $field['id']; ?>]" id="field_<?php echo $field['id']; ?>" 
                           class="form-control" placeholder="请输入<?php echo $field['label']; ?>" <?php echo $field['required'] ? 'required' : ''; ?>>
                    
                    <?php endif; ?>
                    
                    <?php if (!empty($field['hint'])): ?>
                    <div class="form-hint"><?php echo $field['hint']; ?></div>
                    <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <div class="form-panel">
                <div class="form-group required">
                    <label for="contact_name" class="form-label">联系人</label>
                    <div class="form-right">
                        <input type="text" name="contact_name" id="contact_name" class="form-control" placeholder="请输入联系人姓名" value="<?php echo isset($post['contact_name']) ? $post['contact_name'] : ''; ?>" required>
                    </div>
                </div>
                
                <div class="form-group required">
                    <label for="mobile" class="form-label">联系电话</label>
                    <div class="form-right">
                        <input type="tel" value="13800138000" name="contact_mobile" id="mobile" class="form-control" placeholder="请输入手机号码" value="<?php echo isset($post['contact_mobile']) ? $post['contact_mobile'] : ''; ?>" required pattern="[0-9]{11}">
                    </div>
                </div>
                <div class="form-group">
                    <label for="contact_address" class="form-label">详细地址</label>
                    <div class="form-right">
                        <input type="text" name="contact_address" id="contact_address" class="form-control" placeholder="请输入详细地址（选填）" value="<?php echo isset($post['contact_address']) ? $post['contact_address'] : ''; ?>">
                    </div>
                </div>
                <div class="form-group">
                    <label for="contact_weixin" class="form-label">微信号</label>
                    <div class="form-right">
                        <div class="weixin-input-group">
                            <input type="text" name="contact_weixin" id="contact_weixin" class="form-control" placeholder="微信号（选填）" value="<?php echo isset($post['contact_weixin']) ? $post['contact_weixin'] : ''; ?>">
                            <label class="checkbox weixin-checkbox">
                                <input type="checkbox" id="weixin_same"> 与手机相同
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 添加隐藏的验证码字段，确保表单提交不报错 -->
                <input type="hidden" name="sms_code" value="123456">
                
                <div class="form-group required">
                    <label for="password" class="form-label">管理密码</label>
                    <div class="form-right">
                        <input type="password" value="123456" name="password" id="password" class="form-control" placeholder="请设置管理密码" required>
                    </div>
                </div>
                <div class="form-hint-container">
                    <div class="form-hint">设置密码用于后续修改和删除信息</div>
                </div>

                <div class="form-group">
                    <div class="tip-panel posting-notice">
                        <h4 class="notice-title collapsible"><i class="fas fa-exclamation-circle"></i>发布须知 <i class="fas fa-chevron-down toggle-icon"></i></h4>
                        <div class="notice-content collapsed">
                            ①每天限发1条信息,三天内不允许发重复信息②采用信息审核制,须经审核才会显示,应遵守法律法规③后台审核信息时可能会对部分字词句进行调整④上传自拍图片,盗用图片或使用字体侵权风险自担。<br>
                            ⑤招聘不得限定男女性别,涉嫌违法一律不予通过。⑥如继续发布提交信息,视为您已知晓并同意该协议。⑦<a href="http://www.laizhouba.com/help/advertise.html" target="_blank" class="agreement-link">服务协议</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通栏提交按钮 -->
            <div class="submit-group">
                <button type="submit" class="submit-button" id="submit-btn">
                    提交
                </button>
            </div>
        </form>
    </div>
    
    <!-- 加载中遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <div class="loading-text">正在提交中...</div>
        </div>
    </div>
    
    <!-- 提交成功提示 -->
    <div class="success-overlay" id="success-overlay">
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <div class="success-text">提交成功！</div>
            <div class="success-subtext">您的信息已提交，正在等待审核</div>
            <button class="success-btn" id="success-btn">确定</button>
        </div>
    </div>

    <!-- 错误提示弹窗 -->
    <div class="error-overlay" id="error-overlay">
        <div class="error-message-popup">
            <i class="fas fa-exclamation-circle"></i>
            <div class="error-text">提交失败</div>
            <div class="error-subtext" id="error-subtext">请检查输入信息后重试</div>
            <button class="error-btn" id="error-btn">确定</button>
        </div>
    </div>
    
    {include file="footer2.htm"}

    <!-- 底部导航栏 -->
    {include file="navbar.htm"}

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单验证
        const form = document.querySelector('form');
        const loadingOverlay = document.getElementById('loading-overlay');
        const successOverlay = document.getElementById('success-overlay');
        const successBtn = document.getElementById('success-btn');
        const errorOverlay = document.getElementById('error-overlay');
        const errorBtn = document.getElementById('error-btn');
        const errorSubtext = document.getElementById('error-subtext');
        
        // 添加发布须知折叠功能
        const noticeTitle = document.querySelector('.notice-title');
        const noticeContent = document.querySelector('.notice-content');
        const toggleIcon = document.querySelector('.toggle-icon');
        
        noticeTitle.addEventListener('click', function() {
            noticeContent.classList.toggle('collapsed');
            toggleIcon.classList.toggle('rotate');
        });

        // 成功提示框确定按钮事件处理
        successBtn.addEventListener('click', function() {
            window.location.href = '/';
        });

        // 错误提示框确定按钮事件处理
        errorBtn.addEventListener('click', function() {
            errorOverlay.style.display = 'none';
            errorOverlay.classList.remove('show');
        });

        // 显示错误弹窗的函数（全局可用）
        window.showErrorPopup = function(message) {
            errorSubtext.innerHTML = message;
            errorOverlay.style.display = 'flex';
            errorOverlay.classList.add('show');
            // 确保弹窗在最顶层
            errorOverlay.style.zIndex = '10000';
        };
        
        form.addEventListener('submit', function(event) {
            let hasError = false;
            let firstErrorField = null;
            
            // 获取所有必填字段
            const requiredFields = form.querySelectorAll('[required]');
            
            // 检查每个必填字段
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    hasError = true;
                    field.style.borderColor = '#ff3b30';
                    
                    // 记录第一个错误字段
                    if (!firstErrorField) {
                        firstErrorField = field;
                    }
                    
                    // 添加错误提示
                    let errorMsg = document.createElement('div');
                    errorMsg.className = 'form-error';
                    errorMsg.textContent = '请填写' + field.closest('.form-group').querySelector('.form-label').textContent.trim();
                    
                    // 移除之前的错误提示
                    let existingError = field.parentNode.querySelector('.form-error');
                    if (existingError) {
                        existingError.remove();
                    }
                    
                    // 添加新的错误提示
                    field.parentNode.appendChild(errorMsg);
                } else {
                    field.style.borderColor = '#eee';
                    
                    // 移除错误提示
                    let existingError = field.parentNode.querySelector('.form-error');
                    if (existingError) {
                        existingError.remove();
                    }
                }
            });
            
            // 如果有错误，阻止表单提交并滚动到第一个错误字段
            if (hasError) {
                event.preventDefault();
                if (firstErrorField) {
                    firstErrorField.focus();
                    firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                return false;
            }
            
            // 显示加载中提示
            loadingOverlay.classList.add('show');
            
            // 使用AJAX提交表单
            event.preventDefault();

            const formData = new FormData(form);

            // 使用处理后的文件替换原始文件
            if (processedFiles.length > 0) {
                // 移除原始文件
                formData.delete('images[]');

                // 添加处理后的文件
                processedFiles.forEach((file, index) => {
                    if (file) {
                        formData.append('images[]', file);
                    }
                });
            }
            
            fetch(form.action, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => {
                // 隐藏加载中
                loadingOverlay.classList.remove('show');
                
                // 检查响应状态
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                
                // 尝试解析JSON
                return response.text().then(text => {
                    if (!text) {
                        throw new Error('服务器没有返回任何内容');
                    }
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Response text:', text);
                        throw new Error('服务器响应格式错误');
                    }
                });
            })
            .then(data => {
                if (data.success) {
                    // 显示成功提示
                    successOverlay.classList.add('show');
                    
                    // 直接设置点击事件，避免重复添加监听器
                    successBtn.onclick = function() {
                        if (data.detail_url) {
                            window.location.href = data.detail_url;
                        } else {
                            window.location.href = '/';
                        }
                    };
                } else {
                    // 显示错误信息
                    showErrorPopup(data.message || '提交失败，请重试');
                }
            })
            .catch(error => {
                // 隐藏加载中
                loadingOverlay.classList.remove('show');

                // 显示友好的错误提示
                showErrorPopup('提交失败：' + (error.message || '请稍后重试'));
            });
        });
        
        // 移动端图片上传预览和压缩
        const input = document.getElementById('image_uploads');
        const preview = document.getElementById('image-previews');

        if (!input || !preview) {
            return;
        }

        // 初始化移动端图片压缩器
        let mobileCompressor;

        if (typeof MobileImageCompressor !== 'undefined') {
            mobileCompressor = new MobileImageCompressor({
                maxWidth: 1200,
                maxHeight: 1200,
                quality: 0.7,
                targetSize: {$upload_config.max_size} * 1024 * 1024,
                maxSize: 8 * 1024 * 1024,
                enableExifRotation: true,
                showProgress: true
            });
        } else {
            mobileCompressor = new SimpleMobileCompressor();
        }

        // 存储处理后的文件
        let processedFiles = [];

        // 简化的拍照图片处理函数
        async function processCamera(file) {
            return new Promise((resolve, reject) => {
                // 如果文件小于2MB，直接返回原文件
                if (file.size < 2 * 1024 * 1024) {
                    resolve(file);
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 计算压缩后的尺寸
                            let { width, height } = img;
                            const maxSize = 1200;

                            if (width > maxSize || height > maxSize) {
                                const ratio = Math.min(maxSize / width, maxSize / height);
                                width = Math.round(width * ratio);
                                height = Math.round(height * ratio);
                            }

                            canvas.width = width;
                            canvas.height = height;

                            // 绘制图片
                            ctx.drawImage(img, 0, 0, width, height);

                            // 转换为Blob
                            canvas.toBlob(function(blob) {
                                if (blob) {
                                    // 创建新文件，保持原文件名但改为jpg格式
                                    const newFileName = file.name.replace(/\.[^.]+$/, '.jpg');
                                    const processedFile = new File([blob], newFileName, {
                                        type: 'image/jpeg',
                                        lastModified: Date.now()
                                    });
                                    resolve(processedFile);
                                } else {
                                    resolve(file); // 处理失败时返回原文件
                                }
                            }, 'image/jpeg', 0.8);

                        } catch (error) {
                            resolve(file); // 出错时返回原文件
                        }
                    };
                    img.onerror = () => resolve(file); // 加载失败时返回原文件
                    img.src = e.target.result;
                };
                reader.onerror = () => resolve(file); // 读取失败时返回原文件
                reader.readAsDataURL(file);
            });
        }

        // 更新input的文件
        function updateInputWithProcessedFile(processedFile, index) {
            processedFiles[index] = processedFile;
        }

        // 更新input文件列表
        function updateInputFiles() {
            // 过滤掉undefined的文件
            processedFiles = processedFiles.filter(file => file);
        }

        // 简化的图片选择处理
        function handleImageSelection(event) {
            const files = Array.from(this.files);

            if (files.length === 0) {
                return;
            }

            // 限制上传数量
            var maxCount = {$upload_config.max_count};
            if (files.length > maxCount) {
                showErrorPopup('最多只能上传' + maxCount + '张图片');
                this.value = '';
                return;
            }

            // 清空预览区域和处理后的文件数组
            while(preview.firstChild) {
                preview.removeChild(preview.firstChild);
            }
            processedFiles = [];

            // 处理每个文件
            files.forEach(async (file, index) => {
                // 检查文件类型
                if (!file.type.match('image.*')) {
                    return;
                }

                // 检查是否是拍照的图片（通常文件名是image.jpg或类似）
                const isCamera = file.name.toLowerCase().includes('image') || file.size > 3 * 1024 * 1024;

                let processedFile = file;

                // 如果是拍照的图片，进行处理
                if (isCamera) {
                    try {
                        processedFile = await processCamera(file);
                    } catch (error) {
                        // 处理失败时使用原图
                        processedFile = file;
                    }
                }

                // 创建预览
                const div = document.createElement('div');
                div.className = 'image-item';
                div.style.cssText = 'position: relative; display: inline-block; margin: 5px; border: 1px solid #ddd; border-radius: 4px; overflow: hidden;';

                const img = document.createElement('img');
                img.style.cssText = 'width: 80px; height: 80px; object-fit: cover; display: block;';

                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(processedFile);

                const removeBtn = document.createElement('span');
                removeBtn.className = 'remove-image';
                removeBtn.textContent = '×';
                removeBtn.style.cssText = `
                    position: absolute; top: 2px; right: 2px;
                    background: rgba(255,0,0,0.8); color: white;
                    border-radius: 50%; width: 18px; height: 18px;
                    text-align: center; line-height: 18px;
                    cursor: pointer; font-size: 12px;
                `;
                removeBtn.onclick = function() {
                    div.remove();
                    // 同时从input中移除对应的文件
                    updateInputFiles();
                };

                // 添加文件信息
                const fileInfo = document.createElement('div');
                fileInfo.style.cssText = `
                    position: absolute; bottom: 0; left: 0; right: 0;
                    background: rgba(0,0,0,0.7); color: white;
                    font-size: 10px; padding: 2px; text-align: center;
                `;
                const fileSize = (processedFile.size / 1024 / 1024).toFixed(1);
                fileInfo.textContent = `${fileSize}MB${isCamera ? ' 📷' : ''}`;

                div.appendChild(img);
                div.appendChild(removeBtn);
                div.appendChild(fileInfo);
                preview.appendChild(div);

                // 更新input的文件
                updateInputWithProcessedFile(processedFile, index);
            });
        }

        // 绑定图片选择事件
        input.addEventListener('change', handleImageSelection);
        
        // 微信号与手机号同步
        const mobileInput = document.getElementById('mobile');
        const wechatInput = document.getElementById('contact_weixin');
        const wechatSame = document.getElementById('weixin_same');
        
        wechatSame.addEventListener('change', function() {
            if (this.checked) {
                wechatInput.value = mobileInput.value;
                wechatInput.disabled = true;
            } else {
                wechatInput.disabled = false;
            }
        });
        
        mobileInput.addEventListener('input', function() {
            if (wechatSame.checked) {
                wechatInput.value = this.value;
            }
        });
        
        // 分类变更时加载对应的自定义字段
        const categorySelect = document.getElementById('category_id');
        if (categorySelect) {
            categorySelect.addEventListener('change', function() {
                if (this.value) {
                    window.location.href = '/app.php?m=post&category_id=' + this.value;
                }
            });
        }
    });
    </script>
</body>
</html> 