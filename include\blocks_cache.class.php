<?php
/**
 * 内容块缓存管理类
 * 专门处理内容块相关的缓存操作
 */

if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

class BlocksCache {
    private $cache;
    private $cachePrefix = 'block_';
    private $defaultExpire = 86400; // 24小时

    public function __construct() {
        // 使用全局缓存实例
        $this->cache = $GLOBALS['file_cache'];
    }

    /**
     * 从配置获取缓存时效
     */
    private function getCacheExpire() {
        global $config;

        // 检查缓存是否启用
        $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
        if (!$cache_enable) {
            return 0; // 缓存未启用
        }

        // 获取块缓存时效配置，默认24小时
        $cache_blocks = isset($config['cache_blocks']) ? intval($config['cache_blocks']) : 86400;
        return $cache_blocks;
    }

    /**
     * 生成缓存键名
     */
    private function getCacheKey($identifier) {
        return $this->cachePrefix . $identifier;
    }
    
    /**
     * 获取块缓存
     */
    public function getBlockCache($identifier) {
        $key = $this->getCacheKey($identifier);
        return $this->cache->get($key);
    }
    
    /**
     * 设置块缓存
     */
    public function setBlockCache($identifier, $content, $expire = null) {
        $key = $this->getCacheKey($identifier);
        $expire = $expire ?: $this->getCacheExpire();
        return $this->cache->set($key, $content, $expire);
    }
    
    /**
     * 删除单个块缓存
     */
    public function deleteBlockCache($identifier) {
        $key = $this->getCacheKey($identifier);
        return $this->cache->delete($key);
    }
    
    /**
     * 清理所有块缓存
     */
    public function clearAllBlocksCache() {
        $cacheDir = $this->cache->getCacheDir();
        $pattern = $cacheDir . $this->cachePrefix . '*.cache';
        $files = glob($pattern);
        $count = 0;
        
        foreach ($files as $file) {
            if (@unlink($file)) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * 获取所有块缓存信息
     */
    public function getAllBlocksCacheInfo() {
        $cacheDir = $this->cache->getCacheDir();
        $pattern = $cacheDir . $this->cachePrefix . '*.cache';
        $files = glob($pattern);
        $cacheInfo = array();
        
        foreach ($files as $file) {
            $filename = basename($file, '.cache');
            $identifier = str_replace($this->cachePrefix, '', $filename);
            
            $cacheInfo[] = array(
                'identifier' => $identifier,
                'file' => $file,
                'size' => filesize($file),
                'created' => filectime($file),
                'modified' => filemtime($file)
            );
        }
        
        return $cacheInfo;
    }
    
    /**
     * 预热块缓存
     * 重新生成所有启用的块缓存
     */
    public function warmupBlocksCache() {
        global $db;
        
        // 获取所有启用的块
        $sql = "SELECT identifier, content FROM content_blocks WHERE status = 1";
        $result = $db->query($sql);
        
        $count = 0;
        while ($row = $db->fetch_array($result)) {
            if ($this->setBlockCache($row['identifier'], $row['content'])) {
                $count++;
            }
        }
        
        return $count;
    }
    
    /**
     * 检查块缓存是否存在
     */
    public function blockCacheExists($identifier) {
        $key = $this->getCacheKey($identifier);
        return $this->cache->exists($key);
    }
    
    /**
     * 获取块缓存统计信息
     */
    public function getBlocksCacheStats() {
        $cacheInfo = $this->getAllBlocksCacheInfo();
        $totalSize = 0;
        $totalFiles = count($cacheInfo);
        
        foreach ($cacheInfo as $info) {
            $totalSize += $info['size'];
        }
        
        return array(
            'total_files' => $totalFiles,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize),
            'cache_dir' => $this->cache->getCacheDir(),
            'files' => $cacheInfo
        );
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

/**
 * 全局块缓存实例
 */
$GLOBALS['blocks_cache'] = new BlocksCache();

/**
 * 块缓存辅助函数
 */
function blocks_cache_get($identifier) {
    return $GLOBALS['blocks_cache']->getBlockCache($identifier);
}

function blocks_cache_set($identifier, $content, $expire = null) {
    return $GLOBALS['blocks_cache']->setBlockCache($identifier, $content, $expire);
}

function blocks_cache_delete($identifier) {
    return $GLOBALS['blocks_cache']->deleteBlockCache($identifier);
}

function blocks_cache_clear_all() {
    return $GLOBALS['blocks_cache']->clearAllBlocksCache();
}

function blocks_cache_exists($identifier) {
    return $GLOBALS['blocks_cache']->blockCacheExists($identifier);
}

function blocks_cache_warmup() {
    return $GLOBALS['blocks_cache']->warmupBlocksCache();
}

function blocks_cache_stats() {
    return $GLOBALS['blocks_cache']->getBlocksCacheStats();
}
?>
