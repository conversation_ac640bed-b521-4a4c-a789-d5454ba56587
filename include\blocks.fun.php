<?php
/**
 * 内容块调用函数
 * 提供前台调用内容块的功能
 */

if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

// 引入块缓存类
require_once(dirname(__FILE__) . '/blocks_cache.class.php');

/**
 * 获取内容块
 * @param string $identifier 块标识符
 * @param bool $use_cache 是否使用缓存，默认true
 * @return string 块内容，如果不存在返回空字符串
 */
function get_block($identifier, $use_cache = true) {
    global $db;
    
    // 参数验证
    if (empty($identifier) || !is_string($identifier)) {
        return '';
    }
    
    // 清理标识符，只允许字母、数字、下划线和短横线
    $identifier = preg_replace('/[^a-zA-Z0-9_-]/', '', $identifier);
    if (empty($identifier)) {
        return '';
    }
    
    $content = '';
    
    // 如果启用缓存，先尝试从缓存获取
    if ($use_cache) {
        $content = blocks_cache_get($identifier);
        if ($content !== false) {
            return $content;
        }
    }
    
    // 从数据库获取
    $sql = "SELECT content FROM content_blocks WHERE identifier = ? AND status = 1";
    $result = $db->query($sql, [$identifier]);
    
    if ($result && $row = $db->fetch_array($result)) {
        $content = $row['content'];
        
        // 如果启用缓存，保存到缓存
        if ($use_cache && !empty($content)) {
            blocks_cache_set($identifier, $content);
        }
    }
    
    return $content;
}

/**
 * 显示内容块
 * @param string $identifier 块标识符
 * @param bool $use_cache 是否使用缓存，默认true
 */
function show_block($identifier, $use_cache = true) {
    echo get_block($identifier, $use_cache);
}

/**
 * 检查内容块是否存在
 * @param string $identifier 块标识符
 * @return bool
 */
function block_exists($identifier) {
    global $db;
    
    if (empty($identifier)) {
        return false;
    }
    
    // 清理标识符
    $identifier = preg_replace('/[^a-zA-Z0-9_-]/', '', $identifier);
    if (empty($identifier)) {
        return false;
    }
    
    $sql = "SELECT id FROM content_blocks WHERE identifier = ? AND status = 1";
    $result = $db->query($sql, [$identifier]);
    
    return $result && $db->fetch_array($result);
}

/**
 * 获取所有可用的内容块列表
 * @return array 块列表
 */
function get_all_blocks() {
    global $db;
    
    $sql = "SELECT id, name, identifier, description, sort_order, created_at, updated_at 
            FROM content_blocks 
            WHERE status = 1 
            ORDER BY sort_order ASC, id ASC";
    $result = $db->query($sql);
    
    $blocks = array();
    while ($row = $db->fetch_array($result)) {
        $blocks[] = $row;
    }
    
    return $blocks;
}

/**
 * 刷新单个块的缓存
 * @param string $identifier 块标识符
 * @return bool
 */
function refresh_block_cache($identifier) {
    if (empty($identifier)) {
        return false;
    }
    
    // 先删除旧缓存
    blocks_cache_delete($identifier);
    
    // 重新获取并缓存
    $content = get_block($identifier, true);
    
    return !empty($content);
}

/**
 * 批量刷新所有块缓存
 * @return int 刷新的块数量
 */
function refresh_all_blocks_cache() {
    return blocks_cache_warmup();
}

/**
 * 解析模板中的块标签
 * 将 {block:identifier} 替换为实际内容
 * @param string $template 模板内容
 * @return string 解析后的内容
 */
function parse_block_tags($template) {
    if (empty($template)) {
        return $template;
    }
    
    // 匹配 {block:identifier} 格式的标签
    $pattern = '/\{block:([a-zA-Z0-9_-]+)\}/';
    
    return preg_replace_callback($pattern, function($matches) {
        $identifier = $matches[1];
        return get_block($identifier);
    }, $template);
}

/**
 * 获取块的详细信息
 * @param string $identifier 块标识符
 * @return array|false 块信息数组或false
 */
function get_block_info($identifier) {
    global $db;
    
    if (empty($identifier)) {
        return false;
    }
    
    // 清理标识符
    $identifier = preg_replace('/[^a-zA-Z0-9_-]/', '', $identifier);
    if (empty($identifier)) {
        return false;
    }
    
    $sql = "SELECT * FROM content_blocks WHERE identifier = ?";
    $result = $db->query($sql, [$identifier]);
    
    if ($result && $row = $db->fetch_array($result)) {
        return $row;
    }
    
    return false;
}

/**
 * 清理块缓存
 * @param string $identifier 块标识符，为空则清理所有
 * @return bool|int
 */
function clear_block_cache($identifier = '') {
    if (empty($identifier)) {
        return blocks_cache_clear_all();
    } else {
        return blocks_cache_delete($identifier);
    }
}

/**
 * 获取块缓存统计信息
 * @return array
 */
function get_blocks_cache_stats() {
    return blocks_cache_stats();
}
?>
