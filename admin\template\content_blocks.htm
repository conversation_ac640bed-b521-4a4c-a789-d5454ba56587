{include file="header.htm"}

<!-- 页面标题 -->
    <div class="page-title">
        <h1><i class="fas fa-cube"></i> 内容块管理</h1>
        {if $action == 'list'}
        <div class="page-actions">
            <button class="btn btn-primary" onclick="location.href='content_blocks.php?action=add'">
                <i class="fas fa-plus"></i> 添加内容块
            </button>
            <button class="btn btn-outline" onclick="if(confirm('确定要清除所有块缓存吗？')) location.href='content_blocks.php?action=clear_cache'">
                <i class="fas fa-trash"></i> 清除缓存
            </button>
            <button class="btn btn-outline" onclick="location.href='content_blocks.php?action=warmup_cache'">
                <i class="fas fa-sync"></i> 预热缓存
            </button>
        </div>
        {/if}
    </div>
    {if $message}
    <div class="section">
        <div class="alert alert-{if $message_type == 'success'}success{elseif $message_type == 'error'}danger{else}info{/if}">
            <i class="fas fa-{if $message_type == 'success'}check-circle{elseif $message_type == 'error'}exclamation-circle{else}info-circle{/if}"></i>
            <div>{$message}</div>
        </div>
    </div>
    {/if}

    {if $action == 'list'}
    <!-- 列表页面 -->
    <div class="section">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">内容块列表 <span class="record-count">(共 {if $blocks}{$blocks|count}{else}0{/if} 条记录)</span></h3>
                <div class="card-actions">
                    {if $cache_stats}
                    <span class="tag tag-success">缓存文件: {$cache_stats.total_files}</span>
                    <span class="tag tag-warning">缓存大小: {$cache_stats.total_size_formatted}</span>
                    {/if}
                </div>
            </div>
            {if $blocks}
            <div class="table-responsive">
                <table class="table table-enhanced">
                    <thead>
                        <tr>
                            <th width="60" class="text-center">ID</th>
                            <th>块名称</th>
                            <th width="150">调用标识符</th>
                            <th>描述</th>
                            <th width="80" class="text-center">状态</th>
                            <th width="80" class="text-center">排序</th>
                            <th width="140" class="text-center">创建时间</th>
                            <th width="200" class="text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach $blocks as $block}
                        <tr class="table-row">
                            <td class="text-center table-id">{$block.id}</td>
                            <td>
                                <div class="table-title">
                                    <span class="title-link">{$block.name}</span>
                                </div>
                            </td>
                            <td>
                                <div class="copy-code-wrapper">
                                    <div class="code-display" onclick="copyToClipboard(this)" title="点击复制标签">
                                        <code class="copy-target">{literal}{block:{/literal}{$block.identifier}{literal}}{/literal}</code>
                                        <i class="fas fa-copy copy-icon"></i>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="table-subtitle">{if $block.description}{$block.description}{else}-{/if}</div>
                            </td>
                            <td class="text-center">
                                {if $block.status}
                                <span class="status-tag success">启用</span>
                                {else}
                                <span class="status-tag danger">禁用</span>
                                {/if}
                            </td>
                            <td class="text-center table-number">{$block.sort_order}</td>
                            <td class="text-center table-time">{$block.created_date} <small>{$block.created_time}</small></td>
                            <td>
                                <div class="action-buttons-enhanced">
                                    <a href="content_blocks.php?action=edit&id={$block.id}" class="btn-action btn-edit">
                                        <i class="fas fa-edit"></i>
                                        <span>编辑</span>
                                    </a>
                                    <a href="content_blocks.php?action=toggle_status&id={$block.id}"
                                       class="btn-action btn-{if $block.status}warning{else}approve{/if}"
                                       onclick="return confirm('确定要{if $block.status}禁用{else}启用{/if}这个内容块吗？')">
                                        <i class="fas fa-{if $block.status}pause{else}play{/if}"></i>
                                        <span>{if $block.status}禁用{else}启用{/if}</span>
                                    </a>
                                    <a href="content_blocks.php?action=delete&id={$block.id}"
                                       class="btn-action btn-delete btn-confirm"
                                       onclick="return confirm('确定要删除这个内容块吗？删除后无法恢复！')">
                                        <i class="fas fa-trash"></i>
                                        <span>删除</span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {/foreach}
                    </tbody>
                    </table>
            </div>
            {else}
            <div class="empty-state">
                <i class="fas fa-cube"></i>
                <h3>暂无内容块</h3>
                <p>还没有创建任何内容块，点击上方按钮开始添加。</p>
                <button class="btn btn-primary" onclick="location.href='content_blocks.php?action=add'">
                    <i class="fas fa-plus"></i> 添加第一个内容块
                </button>
            </div>
            {/if}
        </div>
    </div>

    {if $blocks}
    <!-- 使用说明 -->
    <div class="section">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">使用说明</h3>
            </div>
            <div class="card-body">
                <div class="quick-actions-grid">
                    <div class="quick-action-item">
                        <div class="quick-action-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-code"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4>模板中调用</h4>
                            <p>在模板文件中使用标签：<br><code>{literal}{block:标识符}{/literal}</code></p>
                        </div>
                    </div>
                    <div class="quick-action-item">
                        <div class="quick-action-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <i class="fas fa-terminal"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4>PHP中调用</h4>
                            <p>在PHP代码中使用函数：<br><code>echo get_block('标识符');</code></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {/if}

    {elseif $action == 'add' || $action == 'edit'}
    <!-- 添加/编辑页面 -->
    <div class="section">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">{if $action == 'add'}添加内容块{else}编辑内容块{/if}</h3>
            </div>
            <div class="card-body">
                <form method="post" action="content_blocks.php" class="form-horizontal" data-validate>
                    <input type="hidden" name="action" value="{$action}">
                    {if $action == 'edit'}
                    <input type="hidden" name="id" value="{$id}">
                    {/if}

                    <div class="form-group">
                        <label class="form-label">块名称 *</label>
                        <div class="form-field">
                            <input type="text" class="form-control" name="name"
                                   value="{if $block_data && $block_data.name}{$block_data.name}{/if}" required placeholder="请输入块名称">
                        </div>
                        <div class="form-help">
                            <span class="help-text">给内容块起一个容易识别的名称</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">调用标识符 *</label>
                        <div class="form-field">
                            <input type="text" class="form-control" name="identifier"
                                   value="{if $block_data && $block_data.identifier}{$block_data.identifier}{/if}" required
                                   pattern="[a-zA-Z0-9_-]+" placeholder="例如：footer_nav">
                        </div>
                        <div class="form-help">
                            <span class="help-text">用于调用的唯一标识符，只能包含字母、数字、下划线和短横线</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">描述说明</label>
                        <div class="form-field">
                            <input type="text" class="form-control" name="description"
                                   value="{if $block_data && $block_data.description}{$block_data.description}{/if}" placeholder="简单描述这个内容块的用途">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">HTML内容 *</label>
                        <div class="form-field">
                            <textarea class="form-textarea" name="content" rows="10" required placeholder="请输入HTML代码">{if $block_data && $block_data.content}{$block_data.content}{/if}</textarea>
                        </div>
                        <div class="form-help">
                            <span class="help-text">输入HTML代码，支持所有HTML标签</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">状态</label>
                        <div class="form-field">
                            <label class="form-check">
                                <input type="radio" name="status" value="1" {if !$block_data || !isset($block_data.status) || $block_data.status == 1}checked{/if}>
                                <span class="form-check-label">启用</span>
                            </label>
                            <label class="form-check">
                                <input type="radio" name="status" value="0" {if $block_data && isset($block_data.status) && $block_data.status == 0}checked{/if}>
                                <span class="form-check-label">禁用</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">排序</label>
                        <div class="form-field">
                            <input type="number" class="form-control" name="sort_order"
                                   value="{if $block_data && isset($block_data.sort_order)}{$block_data.sort_order}{else}0{/if}" min="0" placeholder="0">
                        </div>
                        <div class="form-help">
                            <span class="help-text">数字越小排序越靠前</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> {if $action == 'add'}添加{else}更新{/if}
                        </button>
                        <button type="button" class="btn btn-outline" onclick="location.href='content_blocks.php'">
                            <i class="fas fa-times"></i> 取消
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {/if}

<style>
.copy-code-wrapper {
    width: 100%;
}

.code-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 180px;
    max-width: 220px;
}

.code-display:hover {
    background: #e9ecef;
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.code-display code {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #495057;
    background: none;
    padding: 0;
    border: none;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.copy-icon {
    color: #6c757d;
    font-size: 12px;
    margin-left: 8px;
    transition: color 0.2s;
}

.code-display:hover .copy-icon {
    color: #007bff;
}

.code-display.copied {
    background: #d4edda;
    border-color: #28a745;
}

.code-display.copied .copy-icon {
    color: #28a745;
}

.code-display.copied .copy-icon:before {
    content: "\f00c"; /* fa-check */
}

/* 工具提示 */
.code-display[title]:hover:after {
    content: attr(title);
    position: absolute;
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    margin-top: 25px;
    margin-left: -20px;
}
</style>

<script>
function copyToClipboard(element) {
    // 如果点击的是按钮，获取父元素；如果是代码区域，直接使用
    const codeDisplay = element.classList.contains('code-display') ? element : element.closest('.code-display');
    const codeElement = codeDisplay.querySelector('.copy-target');
    const text = codeElement.textContent.trim();

    // 使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(function() {
            showCopySuccess(codeDisplay);
        }).catch(function(err) {
            // 如果失败，使用传统方法
            fallbackCopyTextToClipboard(text, codeDisplay);
        });
    } else {
        // 使用传统方法
        fallbackCopyTextToClipboard(text, codeDisplay);
    }
}

function fallbackCopyTextToClipboard(text, codeDisplay) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // 避免滚动到底部
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess(codeDisplay);
        } else {
            showCopyError(codeDisplay);
        }
    } catch (err) {
        showCopyError(codeDisplay);
    }

    document.body.removeChild(textArea);
}

function showCopySuccess(codeDisplay) {
    const originalTitle = codeDisplay.title;
    codeDisplay.classList.add('copied');
    codeDisplay.title = '已复制到剪贴板!';

    // 显示复制成功的提示
    const toast = document.createElement('div');
    toast.textContent = '已复制: ' + codeDisplay.querySelector('.copy-target').textContent;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 10px 15px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 9999;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    `;
    document.body.appendChild(toast);

    setTimeout(function() {
        codeDisplay.classList.remove('copied');
        codeDisplay.title = originalTitle;
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 2000);
}

function showCopyError(codeDisplay) {
    const originalTitle = codeDisplay.title;
    codeDisplay.style.background = '#f8d7da';
    codeDisplay.style.borderColor = '#dc3545';
    codeDisplay.title = '复制失败，请手动复制';

    setTimeout(function() {
        codeDisplay.style.background = '';
        codeDisplay.style.borderColor = '';
        codeDisplay.title = originalTitle;
    }, 2000);
}
</script>

{include file="footer.htm"}
