<?php
// 定义安全常量（如果未定义）
if (!defined('IN_BTMPS')) {
    define('IN_BTMPS', true);
}

// 引入公共文件
require_once dirname(__DIR__) . '/include/common.inc.php';

// 获取分类ID或拼音参数并进行严格验证
$catId = isset($_GET['id']) ? filter($_GET['id'], 'int') : 0;
$catPinyin = isset($_GET['pinyin']) ? filter($_GET['pinyin']) : '';

// 验证catId参数，确保为正整数
if ($catId < 0 || $catId > 9999) {
    header("HTTP/1.0 400 Bad Request");
    display('error.htm', ['error_message' => '无效的分类ID']);
    exit;
}

// 更严格验证catPinyin参数，确保只包含安全字符且长度合理
if (!empty($catPinyin)) {
    if (!preg_match('/^[a-zA-Z0-9_-]{1,30}$/', $catPinyin)) {
        header("HTTP/1.0 400 Bad Request");
        display('error.htm', ['error_message' => '无效的分类参数']);
        exit;
    }
}

// 至少需要有一个有效的分类标识
if ($catId == 0 && empty($catPinyin)) {
    header("HTTP/1.0 400 Bad Request");
    display('error.htm', ['error_message' => '缺少分类参数']);
    exit;
}

// 获取并验证页码和分页参数
$page = isset($_GET['page']) ? filter($_GET['page'], 'int') : 1;

// 更严格的页码验证
if ($page <= 0) {
    $page = 1; // 使用默认值替代无效输入
}

// 使用配置的分页大小，而不是硬编码值
$perPage = isset($config['list_page_size']) ? intval($config['list_page_size']) : 20;

// 获取并验证区域筛选参数
$areaId = isset($_GET['area']) ? filter($_GET['area'], 'int') : 0;

// 更严格的区域ID验证
if ($areaId < 0 || $areaId > 9999) {
    $areaId = 0; // 使用默认值替代无效输入
}

// 以下是正常的页面生成逻辑
// 使用缓存获取分类和区域数据
$allCategories = $GLOBALS['cached_categories'];
$allRegions = isset($GLOBALS['cached_areas']) ? $GLOBALS['cached_areas'] : (isset($GLOBALS['cached_regions']) ? $GLOBALS['cached_regions'] : array());

// 创建高效的分类映射表（一次性构建，避免重复循环）
$categoryMaps = buildCategoryMaps($allCategories);
$pinyinToIdMap = $categoryMaps['pinyin_to_id'];
$idToCategoryMap = $categoryMaps['id_to_category'];
$parentToChildrenMap = $categoryMaps['parent_to_children'];

// 根据拼音查找分类ID (使用映射表直接查找，O(1)复杂度)
if ($catPinyin && !$catId) {
    $catId = isset($pinyinToIdMap[$catPinyin]) ? $pinyinToIdMap[$catPinyin] : 0;
}

// 如果没有找到有效分类ID，显示404
if (!$catId) {
    header("HTTP/1.0 404 Not Found");
    display('404.htm');
    exit;
}

// 查找分类信息（直接通过哈希映射获取，O(1)复杂度）
$category = isset($idToCategoryMap[$catId]) ? $idToCategoryMap[$catId] : null;

// 如果缓存中没有找到分类信息，显示404
if (!$category) {
    header("HTTP/1.0 404 Not Found");
    display('404.htm');
    exit;
}

// 处理区域数据
$areaSelection = processAreaSelection($areaId, $allRegions);
$showCities = $areaSelection['showCities'];
$currentProvince = $areaSelection['currentProvince'];
$isProvinceArea = $areaSelection['isProvinceArea'];

// 生成区域导航数据
$area_arr = buildAreaNavigation($category, $showCities, $currentProvince, $areaId, $allRegions);

// 获取缓存设置
$cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
$cache_list_time = isset($config['cache_list']) ? intval($config['cache_list']) : 1800;

// 构建缓存键名（区分不同端、分类、页码、区域）
$template_suffix = TEMPLATE_DIR; // pc, m, wx, app
$cache_key = "category_list_{$template_suffix}_{$catId}_{$page}_{$perPage}_{$areaId}";

$cached_data = false;
if ($cache_enable && $cache_list_time > 0) {
    // 尝试从缓存获取列表数据
    $cached_data = cache_get($cache_key);
}

if ($cached_data !== false) {
    // 从缓存中获取数据
    $posts = $cached_data['posts'];
    $totalPosts = $cached_data['totalPosts'];
} else {
    // 缓存不存在，从数据库获取数据

    // 获取帖子数据
    $posts = getCategoryPostsDirectly($catId, $page, $perPage, 0, $areaId, $isProvinceArea);

    // 使用缓存获取总数
    $totalPosts = getCachedCategoryPostsCount($catId, 0, $areaId, $isProvinceArea);

    // 如果启用缓存，将数据缓存起来
    if ($cache_enable && $cache_list_time > 0) {
        $cache_data = array(
            'posts' => $posts,
            'totalPosts' => $totalPosts
        );
        cache_set($cache_key, $cache_data, $cache_list_time);
    }
}

// 计算总页数，不再限制最大结果数量
$totalCount = $totalPosts;
$totalPages = ceil($totalCount / $perPage);

// 确保当前页不超出总页数
if ($page > $totalPages && $totalPages > 0) {
    $page = $totalPages;
}

// 预处理分类信息，进行HTML转义
$category['name'] = htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8');
$category['pinyin'] = htmlspecialchars($category['pinyin'], ENT_QUOTES, 'UTF-8');

// 确保分类数据完整
if (!isset($category['id'])) {
    $category['id'] = $catId; // 使用从URL获取的分类ID
}

// 获取父分类ID和信息
$parent_id = $category['parent_id'];
$parent_category = null;
$is_secondary_category = ($parent_id > 0);

// 获取父级分类（使用哈希映射，O(1)复杂度）
if ($is_secondary_category) {
    $parent_category = isset($idToCategoryMap[$parent_id]) ? $idToCategoryMap[$parent_id] : null;
    if ($parent_category) {
        $parent_category['name'] = htmlspecialchars($parent_category['name'], ENT_QUOTES, 'UTF-8');
        $parent_category['pinyin'] = htmlspecialchars($parent_category['pinyin'], ENT_QUOTES, 'UTF-8');
    }
}

// 设置URL基础
$baseUrl = buildCategoryUrl($category['pinyin'], 0, $areaId);
$parentUrl = $parent_category ? buildCategoryUrl($parent_category['pinyin'], 0, $areaId) : '';

// 获取子分类（使用预构建的映射表，避免循环查找）
$subCategories = [];
if ($is_secondary_category) {
    // 如果当前是二级分类，获取同级分类
    $subCategories = isset($parentToChildrenMap[$parent_id]) ? $parentToChildrenMap[$parent_id] : [];
} else {
    // 获取当前一级分类的子分类
    $subCategories = isset($parentToChildrenMap[$catId]) ? $parentToChildrenMap[$catId] : [];
}

// 处理子分类的URL和当前标记
foreach ($subCategories as &$subCat) {
    $subCat['url'] = buildCategoryUrl($subCat['pinyin'], 0, $areaId);
    
    // 对于二级分类，标记当前项
    if ($is_secondary_category) {
        $subCat['is_current'] = ($subCat['id'] == $category['id']);
    }
    
    // HTML转义
    foreach ($subCat as $key => $value) {
        if (is_string($value)) {
            $subCat[$key] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        }
    }
}
unset($subCat);

// 生成分页数据
$pagination = buildPagination($page, $totalPages, '{page}', 7, function($page) use ($category, $areaId) {
    // 检查category数组是否包含必要的字段
    if (isset($category) && isset($category['pinyin'])) {
        return buildCategoryUrl($category['pinyin'], 0, $areaId, $page);
    }
    // 如果缺少必要数据，返回默认URL
    return "/?page={$page}";
});

// 为所有终端处理分页/加载更多选项
$simple_pagination = '';
$ajax_loadmore = '';
global $config;
$pagination_mode = isset($config['mobile_pagination_mode']) ? $config['mobile_pagination_mode'] : 'pagination';

// 生成简化版分页（只显示上一页和下一页）
$simple_pagination = buildSimplePagination($page, $totalPages, '{page}', function($page) use ($category, $areaId) {
    // 检查category数组是否包含必要的字段
    if (isset($category) && isset($category['pinyin'])) {
        return buildCategoryUrl($category['pinyin'], 0, $areaId, $page);
    }
    // 如果缺少必要数据，返回默认URL
    return "/?page={$page}";
});

// 生成AJAX加载更多按钮所需数据
$ajax_loadmore = '';
if ($page < $totalPages) {
    $next_page_url = '';
    if (isset($category) && isset($category['pinyin'])) {
        $next_page_url = buildCategoryUrl($category['pinyin'], 0, $areaId, $page + 1);
    } else {
        $next_page_url = "/?page=" . ($page + 1);
    }
    $ajax_loadmore = '<div class="pagination-container"><div class="load-more" data-next-page="' . ($page + 1) . '" data-cat-id="' . $catId . '" data-area-id="' . $areaId . '">点击加载更多</div></div>';
} else {
    $ajax_loadmore = '<div class="pagination-container"><div class="load-more-end">已加载全部内容</div></div>';
}

// 根据配置选择分页模式
if ($pagination_mode == 'loadmore' || $pagination_mode == 'infinite') {
    $pagination = $ajax_loadmore;
} else if (TEMPLATE_DIR == 'm') {
    // 移动端使用简化分页
    $pagination = $simple_pagination;
}

// 计算分页起始和结束页码
$start = max(1, $page - 4);
$end = min($totalPages, $page + 4);
$startPage = max(1, $page - 5); // PC版使用更多的分页链接
$endPage = min($totalPages, $page + 5);

// 生成SEO标题
if (!empty($category['seo_title'])) {
    $seo_title = $category['seo_title'];
} else {
    $seo_title = $category['name'];
    $site_name = isset($GLOBALS['settings']['site_name']) ? $GLOBALS['settings']['site_name'] : '分类信息网';
    $seo_title .= ' - ' . $site_name;
}

// 生成SEO关键词
$seo_keywords = !empty($category['seo_keywords']) ? $category['seo_keywords'] : $category['name'];

// 生成SEO描述
$seo_description = !empty($category['seo_description']) ? $category['seo_description'] : $category['name'] . '网上分类信息';

// 选择模板
$template_file = 'category.htm'; // 默认模板

// 检查分类是否指定了自定义模板
if (!empty($category['template'])) {
    // 生成模板路径（根据当前终端类型确定）
    $template_path = TEMPLATE_PATH . TEMPLATE_DIR . '/' . $category['template'];
    
    // 如果模板文件存在，使用自定义模板
    if (file_exists($template_path)) {
        $template_file = $category['template'];
    }
}

// 分配模板变量
assign([
    'category' => $category,
    'parent_category' => $parent_category,
    'is_secondary_category' => $is_secondary_category,
    'current_is_subcategory' => $is_secondary_category,
    'sub_categories' => $subCategories,
    'subCategories' => $subCategories,
    'area_arr' => $area_arr,
    'area_id' => $areaId,
    'areaId' => $areaId,
    'posts' => $posts,
    'page' => $page,
    'per_page' => $perPage,
    'total_pages' => $totalPages,
    'totalPages' => $totalPages,
    'total_count' => $totalCount,
    'start_page' => $startPage,
    'end_page' => $endPage,
    'pagination' => $pagination,
    'base_url' => $baseUrl,
    'baseUrl' => $baseUrl,
    'parent_url' => $parentUrl,
    'parentUrl' => $parentUrl,
    'seo_title' => $seo_title,
    'seo_keywords' => $seo_keywords,
    'seo_description' => $seo_description,
    'showCities' => $showCities,
    'pagination_mode' => $pagination_mode, // 添加分页模式
    'simple_pagination' => $simple_pagination, // 添加简化分页
    'ajax_loadmore' => $ajax_loadmore, // 添加AJAX加载更多按钮
    'current_page' => 'category'
]);

// 使用当前终端类型显示模板
display($template_file); 