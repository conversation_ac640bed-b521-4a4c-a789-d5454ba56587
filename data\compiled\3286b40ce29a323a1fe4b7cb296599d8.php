<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 左侧一级菜单 -->
<div class="menu-primary">
    <div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
        <a href="index.php">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" data-submenu="content">
        <a href="javascript:void(0)">
            <i class="fas fa-file-alt"></i>
            <span>信息</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" data-submenu="news">
        <a href="javascript:void(0)">
            <i class="fas fa-newspaper"></i>
            <span>新闻</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" data-submenu="site">
        <a href="javascript:void(0)">
            <i class="fas fa-globe"></i>
            <span>站点</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" data-submenu="user">
        <a href="javascript:void(0)">
            <i class="fas fa-users"></i>
            <span>用户</span>
        </a>
    </div>

    <div class="menu-item <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" data-submenu="system">
        <a href="javascript:void(0)">
            <i class="fas fa-cog"></i>
            <span>系统</span>
        </a>
    </div>


</div>

<!-- 右侧二级菜单 -->
<div class="menu-secondary">
    <!-- 信息管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['info', 'category', 'region', 'report'])): ?>active<?php endif; ?>" id="submenu-content">
        <div class="submenu-group-title">信息管理</div>
        <div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
            <a href="info.php">
                <i class="fas fa-list"></i>
                <span>信息管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
            <a href="category.php">
                <i class="fas fa-tags"></i>
                <span>分类管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
            <a href="region.php">
                <i class="fas fa-map-marker-alt"></i>
                <span>区域管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
            <a href="report.php">
                <i class="fas fa-flag"></i>
                <span>举报管理</span>
            </a>
        </div>
    </div>

    <!-- 新闻管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['news', 'news_category'])): ?>active<?php endif; ?>" id="submenu-news">
        <div class="submenu-group-title">新闻管理</div>
        <div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
            <a href="news.php">
                <i class="fas fa-edit"></i>
                <span>新闻管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
            <a href="news_category.php">
                <i class="fas fa-folder"></i>
                <span>新闻栏目</span>
            </a>
        </div>
    </div>

    <!-- 站点管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['about', 'links', 'content_blocks'])): ?>active<?php endif; ?>" id="submenu-site">
        <div class="submenu-group-title">站点管理</div>
        <div class="menu-item <?php if($current_page == 'about'): ?>active<?php endif; ?>">
            <a href="about.php">
                <i class="fas fa-info-circle"></i>
                <span>关于我们</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
            <a href="links.php">
                <i class="fas fa-link"></i>
                <span>友情链接</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'content_blocks'): ?>active<?php endif; ?>">
            <a href="content_blocks.php">
                <i class="fas fa-cube"></i>
                <span>内容块管理</span>
            </a>
        </div>
    </div>

    <!-- 用户管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['admin', 'operation_logs', 'mobile_security'])): ?>active<?php endif; ?>" id="submenu-user">
        <div class="submenu-group-title">用户管理</div>
        <div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
            <a href="admin.php">
                <i class="fas fa-user-shield"></i>
                <span>管理员</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
            <a href="operation_logs.php">
                <i class="fas fa-history"></i>
                <span>操作日志</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
            <a href="mobile_security.php">
                <i class="fas fa-shield-alt"></i>
                <span>手机号安全</span>
            </a>
        </div>
    </div>

    <!-- 系统管理子菜单 -->
    <div class="submenu-group <?php if(in_array($current_page, ['setting', 'cache_manager', 'db_backup'])): ?>active<?php endif; ?>" id="submenu-system">
        <div class="submenu-group-title">系统管理</div>
        <div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
            <a href="setting.php">
                <i class="fas fa-sliders-h"></i>
                <span>系统设置</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
            <a href="cache_manager.php">
                <i class="fas fa-memory"></i>
                <span>缓存管理</span>
            </a>
        </div>
        <div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
            <a href="db_backup.php">
                <i class="fas fa-database"></i>
                <span>数据备份</span>
            </a>
        </div>
    </div>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<!-- 引入统一的JavaScript文件 -->
<script src="static/js/admin-core.js?v=<?php echo time(); ?>"></script>


<style>
.cache-mini-card {
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
}

.cache-mini-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.cache-mini-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    margin: 0 auto 8px;
}

.cache-mini-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.cache-mini-title {
    font-size: 11px;
    color: var(--text-secondary);
    line-height: 1.2;
}

.cache-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
    margin-bottom: 16px;
}

/* 按钮样式优化 */
.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #2ea082;
    border-color: #2ea082;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(58, 210, 159, 0.3);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #d39e00;
    border-color: #d39e00;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(238, 163, 3, 0.3);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    background-color: #138496;
    border-color: #138496;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #e02347;
    border-color: #e02347;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(248, 47, 88, 0.3);
}

/* 表格行悬停效果 */
.table tr:hover {
    background-color: rgba(27, 104, 255, 0.02);
}

.table td:first-child {
    width: 140px;
    vertical-align: middle;
    font-weight: 500;
    padding: 16px 12px;
}

.table td:last-child {
    padding: 16px 12px;
}


</style>

<div class="page-title">
    <h1>缓存管理</h1>
    <div class="d-flex gap-2">
        <a href="index.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            返回首页
        </a>
    </div>
</div>

<?php if($message): ?>
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i>
    <?php echo $message ?? ""; ?>
</div>
<?php endif; ?>


<!-- 缓存类型统计 -->
<div class="section">
    <div class="card">
        <h3 class="card-title">
            缓存类型统计
            <small style="margin-left: 15px; color: var(--text-secondary); font-weight: normal; font-size: 13px;">
                <i class="fas fa-file" style="color: var(--primary-color);"></i> <?php echo (isset($cacheInfo['count'])) ? $cacheInfo['count'] : ""; ?> 个文件
                <span style="margin-left: 12px;">
                    <i class="fas fa-hdd" style="color: var(--success-color);"></i> <?php echo (isset($cacheInfo['total_size_formatted'])) ? $cacheInfo['total_size_formatted'] : ""; ?>
                </span>
            </small>
        </h3>
        <div class="cache-row">
            <div class="cache-mini-card">
                <div class="cache-mini-icon" style="background-color: var(--primary-color);">
                    <i class="fas fa-search"></i>
                </div>
                <div class="cache-mini-value"><?php echo (isset($cacheStats['cache_types']['search']['count'])) ? $cacheStats['cache_types']['search']['count'] : ""; ?></div>
                <div class="cache-mini-title">搜索缓存</div>
            </div>

            <div class="cache-mini-card">
                <div class="cache-mini-icon" style="background-color: var(--success-color);">
                    <i class="fas fa-home"></i>
                </div>
                <div class="cache-mini-value"><?php echo (isset($cacheStats['cache_types']['index']['count'])) ? $cacheStats['cache_types']['index']['count'] : ""; ?></div>
                <div class="cache-mini-title">首页缓存</div>
            </div>

            <div class="cache-mini-card">
                <div class="cache-mini-icon" style="background-color: var(--info-color);">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="cache-mini-value"><?php echo (isset($cacheStats['cache_types']['detail']['count'])) ? $cacheStats['cache_types']['detail']['count'] : ""; ?></div>
                <div class="cache-mini-title">详情缓存</div>
            </div>

            <div class="cache-mini-card">
                <div class="cache-mini-icon" style="background-color: #6f42c1;">
                    <i class="fas fa-list"></i>
                </div>
                <div class="cache-mini-value"><?php echo (isset($cacheStats['cache_types']['list']['count'])) ? $cacheStats['cache_types']['list']['count'] : ""; ?></div>
                <div class="cache-mini-title">列表缓存</div>
            </div>

            <div class="cache-mini-card">
                <div class="cache-mini-icon" style="background-color: #fd7e14;">
                    <i class="fas fa-user"></i>
                </div>
                <div class="cache-mini-value"><?php echo (isset($cacheStats['cache_types']['user']['count'])) ? $cacheStats['cache_types']['user']['count'] : ""; ?></div>
                <div class="cache-mini-title">用户缓存</div>
            </div>

            <div class="cache-mini-card">
                <div class="cache-mini-icon" style="background-color: #20c997;">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="cache-mini-value"><?php echo (isset($cacheStats['cache_types']['stats']['count'])) ? $cacheStats['cache_types']['stats']['count'] : ""; ?></div>
                <div class="cache-mini-title">统计缓存</div>
            </div>

            <div class="cache-mini-card">
                <div class="cache-mini-icon" style="background-color: var(--warning-color);">
                    <i class="fas fa-code"></i>
                </div>
                <div class="cache-mini-value"><?php echo (isset($cacheStats['template_cache']['total_files'])) ? $cacheStats['template_cache']['total_files'] : ""; ?></div>
                <div class="cache-mini-title">模板缓存</div>
            </div>

            <div class="cache-mini-card">
                <div class="cache-mini-icon" style="background-color: #6c757d;">
                    <i class="fas fa-ellipsis-h"></i>
                </div>
                <div class="cache-mini-value"><?php echo (isset($cacheStats['cache_types']['misc']['count'])) ? $cacheStats['cache_types']['misc']['count'] : ""; ?></div>
                <div class="cache-mini-title">其他缓存</div>
            </div>
        </div>
    </div>
</div>

<!-- 缓存操作 -->
<div class="section">
    <div class="card">
        <h3 class="card-title">缓存操作</h3>

        <table class="table" style="margin-bottom: 0;">
            <tr>
                <td style="vertical-align: middle; font-weight: 500; color: var(--primary-color);">页面缓存清理</td>
                <td>
                    <a href="?action=clear_index_cache" class="btn btn-sm btn-warning" style="margin-right: 8px;">
                        <i class="fas fa-home"></i>
                        清理首页缓存
                    </a>
                    <a href="?action=clear_list_cache" class="btn btn-sm btn-warning" style="margin-right: 8px;">
                        <i class="fas fa-list"></i>
                        清理列表缓存
                    </a>
                    <a href="?action=clear_detail_cache" class="btn btn-sm btn-warning">
                        <i class="fas fa-file-alt"></i>
                        清理详情缓存
                    </a>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: middle; font-weight: 500; color: var(--success-color);">新闻缓存管理</td>
                <td>
                    <button type="button" class="btn btn-sm btn-warning" style="margin-right: 8px;" onclick="clearNewsCache('news')">
                        <i class="fas fa-newspaper"></i>
                        清理所有新闻缓存
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" style="margin-right: 8px;" onclick="clearNewsCache('news_home')">
                        <i class="fas fa-home"></i>
                        清理新闻首页缓存
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" style="margin-right: 8px;" onclick="clearNewsCache('news_list')">
                        <i class="fas fa-list"></i>
                        清理新闻列表缓存
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="clearNewsCache('news_detail')">
                        <i class="fas fa-file-alt"></i>
                        清理新闻详情缓存
                    </button>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: middle; font-weight: 500; color: var(--info-color);">其他缓存管理</td>
                <td>
                    <a href="?action=clear_search_cache" class="btn btn-sm btn-warning">
                        <i class="fas fa-search"></i>
                        清理搜索缓存
                    </a>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: middle; font-weight: 500; color: var(--success-color);">缓存预热</td>
                <td>
                    <a href="?action=warmup_cache" class="btn btn-sm btn-primary" onclick="return confirm('确定要执行一键预热吗？这将预先生成常用缓存，可能需要1-2分钟时间。')" style="margin-right: 8px; background: linear-gradient(135deg, var(--primary-color) 0%, #0056d6 100%); border: none; font-weight: 600;">
                        <i class="fas fa-fire"></i>
                        一键预热缓存
                    </a>
                    <small style="color: var(--text-secondary);">
                        <i class="fas fa-info-circle" style="color: var(--info-color);"></i>
                        预热首页、热门分类、模板等常用缓存，提升访问速度
                    </small>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: middle; font-weight: 500; color: var(--success-color);">模板缓存管理</td>
                <td>
                    <a href="?action=precompile_templates" class="btn btn-sm btn-success" style="margin-right: 8px;" onclick="return confirm('确定要预编译所有模板吗？这可能需要一些时间。')">
                        <i class="fas fa-cogs"></i>
                        预编译模板
                    </a>
                    <a href="?action=clear_template_cache" class="btn btn-sm btn-warning" style="margin-right: 8px;" onclick="return confirm('确定要清理所有模板缓存吗？')">
                        <i class="fas fa-trash"></i>
                        清理模板缓存
                    </a>
                    <a href="?action=clean_expired_templates" class="btn btn-sm btn-info">
                        <i class="fas fa-broom"></i>
                        清理过期缓存
                    </a>
                </td>
            </tr>
            <tr>
                <td style="vertical-align: middle; font-weight: 500; color: var(--danger-color);">危险操作</td>
                <td>
                    <a href="?action=clear_all" class="btn btn-sm btn-danger" onclick="return confirm('确定要清理所有缓存吗？这将影响网站性能！')" style="background-color: var(--danger-color); border-color: var(--danger-color);">
                        <i class="fas fa-trash-alt"></i>
                        清理所有缓存
                    </a>
                    <small style="margin-left: 12px; color: var(--text-secondary);">
                        <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                        此操作会删除所有缓存文件，可能短暂影响网站性能
                    </small>
                </td>
            </tr>
        </table>
    </div>
</div>

<!-- 使用说明 -->
<div class="section">
    <div class="card">
        <h3 class="card-title">使用说明</h3>
        <ul style="margin: 0; padding-left: 20px; line-height: 1.8;">
            <li><strong style="color: var(--primary-color);">一键预热缓存：</strong>智能预先生成常用缓存文件，包括首页、栏目列表、热门详情页和模板缓存，显著提升网站访问速度</li>
            <li><strong>清理缓存：</strong>删除现有缓存文件，下次访问时会自动重新生成</li>
            <li><strong>预编译模板：</strong>将所有模板文件预先编译为PHP代码，提高页面加载速度</li>
            <li><strong>清理模板缓存：</strong>删除所有已编译的模板文件，下次访问时会重新编译</li>
            <li><strong>清理过期缓存：</strong>只删除已过期的模板缓存文件，保留仍然有效的缓存</li>
            <li><strong>清理所有缓存：</strong>删除所有缓存文件，可能会短暂影响网站性能</li>
        </ul>

        <h4 style="margin-top: 20px; margin-bottom: 10px; color: var(--primary-color);">
            <i class="fas fa-fire" style="margin-right: 6px;"></i>
            一键预热说明
        </h4>
        <div style="background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%); border: 1px solid #d1e7ff; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
            <p style="margin: 0 0 12px 0; color: var(--text-color); font-weight: 500;">一键预热功能会按顺序执行以下操作：</p>
            <ul style="margin: 0; padding-left: 20px; line-height: 1.6; color: var(--text-secondary);">
                <li><strong>首页缓存预热：</strong>生成PC版、手机版、微信版、APP版首页缓存</li>
                <li><strong>栏目列表页预热：</strong>预先生成所有栏目（一级和二级）的第一页列表缓存</li>
                <li><strong>热门详情页预热：</strong>预热最近7天浏览量最高的20个信息详情页缓存</li>
                <li><strong>清理过期缓存：</strong>自动清理24小时前的过期缓存文件（保留基础数据缓存）</li>
                <li><strong>模板预编译：</strong>将所有模板文件编译为PHP代码</li>
            </ul>
            <p style="margin: 12px 0 0 0; color: var(--info-color); font-size: 13px;">
                <i class="fas fa-lightbulb" style="margin-right: 4px;"></i>
                <strong>建议使用场景：</strong>网站部署后、大量内容更新后、或定期维护时执行，与前端定时任务功能完全一致
            </p>
        </div>

        <h4 style="margin-top: 20px; margin-bottom: 10px;">缓存文件命名规则</h4>
        <ul style="margin: 0; padding-left: 20px; line-height: 1.8;">
            <li><strong>详情缓存：</strong>以 <code>detail_</code> 开头，如 <code>detail_123_pc.cache</code>、<code>detail_123_m.cache</code></li>
            <li><strong>列表缓存：</strong>以 <code>category_list_</code> 或 <code>category_posts_v2_</code> 开头</li>
            <li><strong>搜索缓存：</strong>以 <code>search_</code> 开头，如 <code>search_pc_keyword.cache</code>、<code>search_m_keyword.cache</code></li>
            <li><strong>首页缓存：</strong>以 <code>index_page_</code> 开头，如 <code>index_page_pc.cache</code>、<code>index_page_m.cache</code></li>
            <li><strong>用户缓存：</strong>以 <code>user_</code> 开头，如 <code>user_123.cache</code></li>
            <li><strong>统计缓存：</strong>以 <code>category_posts_count_</code> 开头</li>
            <li><strong>模板缓存：</strong>存储在 <code>data/compiled/</code> 目录，以 <code>.php</code> 结尾</li>
        </ul>
    </div>
</div>

<script>
// 新闻缓存清理函数
function clearNewsCache(type) {
    if (!confirm('确定要清理' + getNewsCacheTypeName(type) + '吗？')) {
        return;
    }

    // 显示加载状态
    const button = event.target;
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清理中...';

    // 发送AJAX请求
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            button.disabled = false;
            button.innerHTML = originalText;

            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.success) {
                        alert('✅ ' + response.message);
                        // 刷新页面以更新缓存统计
                        location.reload();
                    } else {
                        alert('❌ ' + response.message);
                    }
                } catch (e) {
                    alert('❌ 清理失败：服务器响应异常');
                }
            } else {
                alert('❌ 清理失败：网络错误');
            }
        }
    };

    xhr.send('action=clear_cache&type=' + type);
}

// 获取缓存类型名称
function getNewsCacheTypeName(type) {
    const names = {
        'news': '所有新闻缓存',
        'news_home': '新闻首页缓存',
        'news_list': '新闻列表缓存',
        'news_detail': '新闻详情缓存'
    };
    return names[type] || '缓存';
}
</script>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- 页面底部信息 -->
    <footer class="admin-footer">
        <div class="footer-content">
            <div class="footer-copyright">&copy; 2024 分类信息网站后台管理系统</div>
            <div class="footer-version">版本 v1.0.0</div>
        </div>
    </footer>
</body>
</html>
